<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="10dp"
    android:background="@android:color/white"
    android:layout_marginBottom="10dp">

    <!-- Checkbox -->
    <CheckBox
        android:id="@+id/checkbox_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <!-- Measurement text -->
    <TextView
        android:id="@+id/historique_item_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Valeur: 120 mg/dL, Heure: 14:30, Type: Avant le repas"
        android:textColor="#000000"
        android:textSize="16sp"
        android:layout_marginStart="10dp" />

</LinearLayout>