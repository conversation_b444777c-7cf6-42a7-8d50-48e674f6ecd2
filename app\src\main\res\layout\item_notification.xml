<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:elevation="4dp"
    android:backgroundTint="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- Icon -->
        <ImageView
            android:id="@+id/notification_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_notification_active"
            android:contentDescription="Notification Icon"
            android:background="@drawable/circle_background" />

        <!-- Text Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="12dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/notification_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Rappel : petit-déjeuner - 2 tranches khobz eddar complet"
                android:textColor="#000000"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/notification_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="08h00"
                android:textColor="#777777"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>