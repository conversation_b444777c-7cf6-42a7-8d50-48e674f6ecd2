package com.example.myapp;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class AlimentationAdapter extends RecyclerView.Adapter<AlimentationAdapter.ViewHolder> {

    private List<AlimentationEntry> alimentationList;

    public AlimentationAdapter(List<AlimentationEntry> alimentationList) {
        this.alimentationList = alimentationList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_alimentation, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AlimentationEntry entry = alimentationList.get(position);
        holder.idAlimentationText.setText("Id_ALIMENTATION: " + entry.idAlimentation);
        holder.descriptionText.setText("Description: " + entry.description);
        holder.dateHeureText.setText("Date et Heure: " + entry.dateHeure);
        holder.impactGlycemieText.setText("Impact sur la glycémie: " + entry.impactGlycemie);
    }

    @Override
    public int getItemCount() {
        return alimentationList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView idAlimentationText, descriptionText, dateHeureText, impactGlycemieText;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            idAlimentationText = itemView.findViewById(R.id.id_alimentation_text);
            descriptionText = itemView.findViewById(R.id.description_text);
            dateHeureText = itemView.findViewById(R.id.date_heure_text);
            impactGlycemieText = itemView.findViewById(R.id.impact_glycemie_text);
        }
    }

    public static class AlimentationEntry {
        String idAlimentation;
        String description;
        String dateHeure;
        String impactGlycemie;

        AlimentationEntry(String idAlimentation, String description, String dateHeure, String impactGlycemie) {
            this.idAlimentation = idAlimentation;
            this.description = description;
            this.dateHeure = dateHeure;
            this.impactGlycemie = impactGlycemie;
        }
    }
}