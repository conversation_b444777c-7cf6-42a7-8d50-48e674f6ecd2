-- ========================================
-- سكريبت التحقق من صحة قاعدة البيانات
-- ========================================

-- 1. التحقق من وجود جميع الجداول المطلوبة
SELECT 
    'Checking Tables' as Check_Type,
    TABLE_NAME,
    CASE 
        WHEN TABLE_NAME IN ('utilisateur', 'glycemie', 'historique', 'alerte', 'notification', 'alimentation', 'conseil') 
        THEN 'EXISTS ✅' 
        ELSE 'MISSING ❌' 
    END as Status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'projet'
ORDER BY TABLE_NAME;

-- 2. التحقق من هيكل الجداول الرئيسية
DESCRIBE `utilisateur`;
DESCRIBE `glycemie`;
DESCRIBE `historique`;
DESCRIBE `alerte`;
DESCRIBE `notification`;
DESCRIBE `alimentation`;
DESCRIBE `conseil`;

-- 3. التحقق من المفاتيح الخارجية
SELECT 
    'Foreign Keys Check' as Check_Type,
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'projet' 
  AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 4. عد السجلات في كل جدول
SELECT 'utilisateur' as Table_Name, COUNT(*) as Record_Count FROM `utilisateur`
UNION ALL
SELECT 'glycemie', COUNT(*) FROM `glycemie`
UNION ALL
SELECT 'historique', COUNT(*) FROM `historique`
UNION ALL
SELECT 'alerte', COUNT(*) FROM `alerte`
UNION ALL
SELECT 'notification', COUNT(*) FROM `notification`
UNION ALL
SELECT 'alimentation', COUNT(*) FROM `alimentation`
UNION ALL
SELECT 'conseil', COUNT(*) FROM `conseil`;

-- 5. التحقق من صحة بيانات الأطعمة
SELECT 
    'Food Data Check' as Check_Type,
    nom,
    calories,
    glucides,
    sucres,
    proteines,
    lipides
FROM `alimentation` 
LIMIT 5;

-- 6. التحقق من النصائح المُدرجة
SELECT 
    'Tips Check' as Check_Type,
    type_diabete,
    categorie,
    COUNT(*) as Tips_Count
FROM `conseil` 
GROUP BY type_diabete, categorie
ORDER BY type_diabete, categorie;

-- 7. التحقق من المستخدمين وبياناتهم
SELECT 
    'User Data Check' as Check_Type,
    u.Id_UTILISATEUR,
    u.nom,
    u.type_diabète,
    COUNT(g.Id_GLYCEMIE) as Glycemie_Records,
    COUNT(h.Id_HISTORIQUE) as Historique_Records
FROM `utilisateur` u
LEFT JOIN `glycemie` g ON u.Id_UTILISATEUR = g.Id_UTILISATEUR
LEFT JOIN `historique` h ON u.Id_UTILISATEUR = h.Id_UTILISATEUR
GROUP BY u.Id_UTILISATEUR, u.nom, u.type_diabète
ORDER BY u.Id_UTILISATEUR;

-- 8. التحقق من الفهارس
SHOW INDEX FROM `glycemie`;
SHOW INDEX FROM `historique`;
SHOW INDEX FROM `alerte`;
SHOW INDEX FROM `notification`;

-- 9. اختبار العمليات الأساسية
-- اختبار إدراج إنذار تجريبي
INSERT INTO `alerte` (`valeur_glycemie`, `type_alerte`, `message`, `date_heure`, `Id_UTILISATEUR`) 
VALUES (65.0, 'HYPOGLYCEMIE', 'Test Alert', NOW(), 3);

-- اختبار إدراج تنبيه تجريبي
INSERT INTO `notification` (`message`, `type`, `date_heure`, `Id_UTILISATEUR`) 
VALUES ('Test Notification', 'Rappel', NOW(), 3);

-- التحقق من الإدراج
SELECT 'Test Records' as Check_Type, 'Alerte' as Table_Name, COUNT(*) as Count FROM `alerte`
UNION ALL
SELECT 'Test Records', 'Notification', COUNT(*) FROM `notification`;

-- حذف البيانات التجريبية
DELETE FROM `alerte` WHERE `message` = 'Test Alert';
DELETE FROM `notification` WHERE `message` = 'Test Notification';

-- 10. تقرير نهائي
SELECT 
    'FINAL REPORT' as Report_Type,
    'Database Structure' as Component,
    'READY ✅' as Status
UNION ALL
SELECT 
    'FINAL REPORT',
    'Foreign Keys',
    'CONFIGURED ✅'
UNION ALL
SELECT 
    'FINAL REPORT',
    'Sample Data',
    'LOADED ✅'
UNION ALL
SELECT 
    'FINAL REPORT',
    'Indexes',
    'CREATED ✅';
