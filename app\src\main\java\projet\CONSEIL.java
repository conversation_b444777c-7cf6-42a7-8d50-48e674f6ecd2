package projet;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import android.util.Log;
import static projet.Shared.con;

/**
 * <AUTHOR>
 */
public class CONSEIL {
    int Id_CONSEIL;
    String titre;
    String contenu;
    String type_diabete; // "Type 1", "Type 2", "Gestationnel", "General"
    String categorie; // "Alimentation", "Exercice", "Medicament", "General"
    String date_creation;
    
    static ArrayList<CONSEIL> conseils = new ArrayList<>();

    // Constructor for creating new conseil (without ID)
    public CONSEIL(String titre, String contenu, String type_diabete, String categorie, String date_creation) {
        this.titre = titre;
        this.contenu = contenu;
        this.type_diabete = type_diabete;
        this.categorie = categorie;
        this.date_creation = date_creation;
    }

    // Constructor for retrieving from database (with ID)
    public CONSEIL(int Id_CONSEIL, String titre, String contenu, String type_diabete, String categorie, String date_creation) {
        this.Id_CONSEIL = Id_CONSEIL;
        this.titre = titre;
        this.contenu = contenu;
        this.type_diabete = type_diabete;
        this.categorie = categorie;
        this.date_creation = date_creation;
    }

    // Getters and setters
    public int getId_CONSEIL() {
        return Id_CONSEIL;
    }

    public void setId_CONSEIL(int Id_CONSEIL) {
        this.Id_CONSEIL = Id_CONSEIL;
    }

    public String getTitre() {
        return titre;
    }

    public void setTitre(String titre) {
        this.titre = titre;
    }

    public String getContenu() {
        return contenu;
    }

    public void setContenu(String contenu) {
        this.contenu = contenu;
    }

    public String getType_diabete() {
        return type_diabete;
    }

    public void setType_diabete(String type_diabete) {
        this.type_diabete = type_diabete;
    }

    public String getCategorie() {
        return categorie;
    }

    public void setCategorie(String categorie) {
        this.categorie = categorie;
    }

    public String getDate_creation() {
        return date_creation;
    }

    public void setDate_creation(String date_creation) {
        this.date_creation = date_creation;
    }

    public static ArrayList<CONSEIL> getConseils() {
        return conseils;
    }

    public static void setConseils(ArrayList<CONSEIL> conseils) {
        CONSEIL.conseils = conseils;
    }

    @Override
    public String toString() {
        return "CONSEIL{" +
                "Id_CONSEIL=" + Id_CONSEIL +
                ", titre='" + titre + '\'' +
                ", contenu='" + contenu + '\'' +
                ", type_diabete='" + type_diabete + '\'' +
                ", categorie='" + categorie + '\'' +
                ", date_creation='" + date_creation + '\'' +
                '}';
    }

    // Database operations
    public static String add(CONSEIL conseil) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("CONSEIL", errorMsg);
            return errorMsg;
        }

        String sql = "INSERT INTO `conseil` (`titre`, `contenu`, `type_diabete`, `categorie`, `date_creation`) VALUES (?, ?, ?, ?, ?)";
        try {
            PreparedStatement stmt = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, conseil.getTitre());
            stmt.setString(2, conseil.getContenu());
            stmt.setString(3, conseil.getType_diabete());
            stmt.setString(4, conseil.getCategorie());
            stmt.setString(5, conseil.getDate_creation());

            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    conseil.setId_CONSEIL(rs.getInt(1));
                }
                return "Conseil ajouté avec succès";
            } else {
                return "Échec de l'ajout du conseil";
            }
        } catch (Exception e) {
            Log.e("CONSEIL", "Error adding conseil: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static ArrayList<CONSEIL> getByDiabetesType(String diabetesType) {
        ArrayList<CONSEIL> conseilList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("CONSEIL", "Database connection failed in getByDiabetesType. Détails: " + Shared.getLastErrorMessage());
            return conseilList;
        }

        String sql = "SELECT * FROM `conseil` WHERE type_diabete = ? OR type_diabete = 'General' ORDER BY date_creation DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, diabetesType);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                CONSEIL conseil = new CONSEIL(
                        rs.getInt("Id_CONSEIL"),
                        rs.getString("titre"),
                        rs.getString("contenu"),
                        rs.getString("type_diabete"),
                        rs.getString("categorie"),
                        rs.getString("date_creation")
                );
                conseilList.add(conseil);
            }
        } catch (Exception e) {
            Log.e("CONSEIL", "Error fetching conseils by diabetes type: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return conseilList;
    }

    public static ArrayList<CONSEIL> getByCategory(String category) {
        ArrayList<CONSEIL> conseilList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("CONSEIL", "Database connection failed in getByCategory. Détails: " + Shared.getLastErrorMessage());
            return conseilList;
        }

        String sql = "SELECT * FROM `conseil` WHERE categorie = ? ORDER BY date_creation DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, category);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                CONSEIL conseil = new CONSEIL(
                        rs.getInt("Id_CONSEIL"),
                        rs.getString("titre"),
                        rs.getString("contenu"),
                        rs.getString("type_diabete"),
                        rs.getString("categorie"),
                        rs.getString("date_creation")
                );
                conseilList.add(conseil);
            }
        } catch (Exception e) {
            Log.e("CONSEIL", "Error fetching conseils by category: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return conseilList;
    }

    public static ArrayList<CONSEIL> getAll() {
        ArrayList<CONSEIL> conseilList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("CONSEIL", "Database connection failed in getAll. Détails: " + Shared.getLastErrorMessage());
            return conseilList;
        }

        String sql = "SELECT * FROM `conseil` ORDER BY date_creation DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                CONSEIL conseil = new CONSEIL(
                        rs.getInt("Id_CONSEIL"),
                        rs.getString("titre"),
                        rs.getString("contenu"),
                        rs.getString("type_diabete"),
                        rs.getString("categorie"),
                        rs.getString("date_creation")
                );
                conseilList.add(conseil);
            }
        } catch (Exception e) {
            Log.e("CONSEIL", "Error fetching all conseils: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return conseilList;
    }

    // Method to initialize default tips
    public static void initializeDefaultTips() {
        // This method can be called to populate the database with default tips
        String currentDate = java.time.LocalDateTime.now().toString();
        
        // Type 1 diabetes tips
        add(new CONSEIL("Surveillance glycémique", 
                "Mesurez votre glycémie plusieurs fois par jour, surtout avant/après les repas et l'activité physique.", 
                "Type 1", "General", currentDate));
        
        add(new CONSEIL("Gestion de l'insuline", 
                "Respectez les doses d'insuline prescrites et apprenez à les ajuster selon votre alimentation et votre glycémie.", 
                "Type 1", "Medicament", currentDate));
        
        // Type 2 diabetes tips
        add(new CONSEIL("Alimentation équilibrée", 
                "Adoptez une alimentation pauvre en sucres et en graisses, riche en légumes, céréales complètes et protéines légères.", 
                "Type 2", "Alimentation", currentDate));
        
        add(new CONSEIL("Activité physique", 
                "Faites au moins 30 minutes d'activité physique par jour (marche, vélo...).", 
                "Type 2", "Exercice", currentDate));
        
        // General tips
        add(new CONSEIL("Hydratation", 
                "Buvez suffisamment d'eau chaque jour et évitez les boissons sucrées.", 
                "General", "General", currentDate));
    }
}
