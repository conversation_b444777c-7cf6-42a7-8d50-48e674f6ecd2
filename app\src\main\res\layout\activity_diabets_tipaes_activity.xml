<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/gradient_background">

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Conseils pour les diabétiques"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:paddingBottom="20dp"
        android:textColor="#000000"/>

    <!-- Buttons Layout -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="10dp">

        <Button
            android:id="@+id/btnType1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Type 1"
            android:textSize="14sp"
            android:backgroundTint="#4CAF50"
            android:layout_marginEnd="5dp"/>

        <Button
            android:id="@+id/btnType2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Type 2"
            android:textSize="14sp"
            android:backgroundTint="#2196F3"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"/>

        <Button
            android:id="@+id/btnGeneral"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Général"
            android:textSize="14sp"
            android:backgroundTint="#FF9800"
            android:layout_marginStart="5dp"/>

    </LinearLayout>

    <!-- Back Button -->
    <Button
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="Retour"
        android:textSize="14sp"
        android:backgroundTint="#607D8B"
        android:layout_marginBottom="10dp"/>

    <!-- TextView to display tips summary -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_weight="0.4">

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="8dp"
            android:text="Choisissez un type de diabète pour afficher les conseils"
            android:textColor="#333333"
            android:textSize="14sp"
            android:padding="10dp"
            android:background="#F5F5F5" />
    </ScrollView>

    <!-- RecyclerView for detailed tips -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewTips"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.6"
        android:layout_marginTop="10dp"
        android:background="#FFFFFF"
        android:padding="5dp" />

</LinearLayout>