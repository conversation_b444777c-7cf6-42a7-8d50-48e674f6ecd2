<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity"
    android:background="@drawable/gradient_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Image et Titre -->
        <ImageView
            android:id="@+id/welcomeImage"
            android:layout_width="140dp"
            android:layout_height="110dp"
            android:scaleType="centerCrop"
            android:src="@drawable/pfc"
            android:contentDescription="Logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Titre avec nom d'utilisateur -->
        <TextView
            android:id="@+id/welcomeName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:text="Bienvenue"
            android:textColor="@color/black"
            android:textSize="24sp"
            app:layout_constraintEnd_toStartOf="@id/welcomeImage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Description personnalisée -->
        <TextView
            android:id="@+id/welcomeDesc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Bienvenue dans votre espace santé"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@id/welcomeImage"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/welcomeName" />

        <!-- Grid des cartes -->
        <GridLayout
            android:id="@+id/cardGrid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:columnCount="2"
            android:rowCount="3"
            android:padding="4dp"
            app:layout_constraintTop_toBottomOf="@id/welcomeImage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Patient (Row 1, Left) -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="6dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/actph"
                        android:contentDescription="Icône Patient"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Utilisateur"
                        android:textColor="@android:color/black"
                        android:textSize="15sp"
                        android:layout_marginTop="6dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Historique (Row 1, Right) -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="6dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/hist"
                        android:contentDescription="Icône Historique"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Historique"
                        android:textColor="@android:color/black"
                        android:textSize="15sp"
                        android:layout_marginTop="6dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Mesure (Row 2, Left) -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="6dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/mesur"
                        android:contentDescription="Icône Glycémie"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Mesure"
                        android:textColor="@android:color/black"
                        android:textSize="15sp"
                        android:layout_marginTop="6dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Alimentation (Row 2, Right) -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="6dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/alim"
                        android:contentDescription="Icône Alimentation"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Alimentation"
                        android:textColor="@android:color/black"
                        android:textSize="15sp"
                        android:layout_marginTop="6dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Paramètres (Row 3, Left) -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="6dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/health"
                        android:contentDescription="Icône Paramètres"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Health Advice"
                        android:textColor="@android:color/black"
                        android:textSize="15sp"
                        android:layout_marginTop="6dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Notification (Row 3, Right) -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_rowWeight="1"
                android:layout_margin="6dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/notifi"
                        android:contentDescription="Icône Notification"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Notification"
                        android:textColor="@android:color/black"
                        android:textSize="15sp"
                        android:layout_marginTop="6dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </GridLayout>

        <!-- Boutons -->
        <Button
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Retour"
            android:textColor="@color/black"
            android:backgroundTint="@color/white"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@id/cardGrid"
            app:layout_constraintStart_toStartOf="parent" />
        <Button
            android:id="@+id/btnLogout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Se déconnecter"
            android:textColor="@color/black"
            android:backgroundTint="@color/white"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@id/cardGrid"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>