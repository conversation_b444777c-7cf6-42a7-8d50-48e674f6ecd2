package com.example.myapp;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import projet.GLYCEMIE;
import projet.Shared;

public class GraphActivity extends AppCompatActivity {

    private static final String TAG = "GraphActivity";
    private LineChart lineChart;
    private List<HistoriqueAdapter.GlycemieEntry> glycemieList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_graph);

        // Initialize the LineChart
        lineChart = findViewById(R.id.glycemie_chart);

        // Load glycemia data from Intent
        loadGlycemieData();

        // Setup the chart
        setupLineChart();
    }

    private void loadGlycemieData() {
        // First try to get data from Intent
        Gson gson = new Gson();
        String json = getIntent().getStringExtra("glycemie_list");
        Log.d(TAG, "Received JSON: " + (json != null ? json : "null"));

        if (json != null && !json.isEmpty()) {
            Type type = new TypeToken<ArrayList<HistoriqueAdapter.GlycemieEntry>>(){}.getType();
            glycemieList = gson.fromJson(json, type);

            if (glycemieList == null) {
                glycemieList = new ArrayList<>();
                Log.w(TAG, "glycemiaList is null, initialized empty");
            } else {
                Log.d(TAG, "Loaded " + glycemieList.size() + " entries from Intent");
                for (HistoriqueAdapter.GlycemieEntry entry : glycemieList) {
                    Log.d(TAG, "Entry - Value: " + entry.valeur + ", DateTime: " + entry.dateTime + ", Timestamp: " + entry.timestamp);
                }
            }
        } else {
            // If no data from Intent, load from database
            Log.d(TAG, "No data from Intent, loading from database");
            loadGlycemieFromDatabase();
        }
    }

    private void loadGlycemieFromDatabase() {
        // Get current user ID
        int userId = Shared.utilisateur != null ? Shared.utilisateur.getId_UTILISATEUR() : -1;
        if (userId == -1) {
            // Try to get user from SharedPreferences
            SharedPreferences prefs = getSharedPreferences("UserPrefs", MODE_PRIVATE);
            String email = prefs.getString("email", "");
            String password = prefs.getString("password", "");

            if (!email.isEmpty() && !password.isEmpty()) {
                new Thread(() -> {
                    try {
                        projet.UTILISATEUR user = projet.UTILISATEUR.authentifier(email, password);
                        if (user != null) {
                            Shared.utilisateur = user;
                            loadUserGlycemieData(user.getId_UTILISATEUR());
                        } else {
                            runOnUiThread(() -> {
                                Toast.makeText(this, "Utilisateur non connecté", Toast.LENGTH_SHORT).show();
                                glycemieList = new ArrayList<>();
                                setupLineChart();
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error authenticating user: " + e.getMessage(), e);
                        runOnUiThread(() -> {
                            Toast.makeText(this, "Erreur de connexion", Toast.LENGTH_SHORT).show();
                            glycemieList = new ArrayList<>();
                            setupLineChart();
                        });
                    }
                }).start();
            } else {
                Toast.makeText(this, "Utilisateur non connecté", Toast.LENGTH_SHORT).show();
                glycemieList = new ArrayList<>();
                setupLineChart();
            }
            return;
        }

        loadUserGlycemieData(userId);
    }

    private void loadUserGlycemieData(int userId) {
        new Thread(() -> {
            try {
                ArrayList<GLYCEMIE> glycemies = GLYCEMIE.getAllForUser(userId);

                // Convert GLYCEMIE objects to GlycemieEntry objects
                ArrayList<HistoriqueAdapter.GlycemieEntry> entries = new ArrayList<>();
                for (GLYCEMIE glycemie : glycemies) {
                    HistoriqueAdapter.GlycemieEntry entry = new HistoriqueAdapter.GlycemieEntry();
                    entry.valeur = String.valueOf(glycemie.getValeur());
                    entry.dateTime = glycemie.getDate_heure();

                    // Convert date string to timestamp
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                        Date date = sdf.parse(glycemie.getDate_heure());
                        entry.timestamp = date != null ? date.getTime() : 0;
                    } catch (Exception e) {
                        Log.e(TAG, "Error parsing date: " + glycemie.getDate_heure(), e);
                        entry.timestamp = 0;
                    }

                    entries.add(entry);
                }

                runOnUiThread(() -> {
                    glycemieList = entries;
                    setupLineChart();
                    Log.d(TAG, "Loaded " + entries.size() + " glycemie records from database");
                });
            } catch (Exception e) {
                Log.e(TAG, "Error loading glycemie data: " + e.getMessage(), e);
                runOnUiThread(() -> {
                    Toast.makeText(this, "Erreur lors du chargement des données", Toast.LENGTH_SHORT).show();
                    glycemieList = new ArrayList<>();
                    setupLineChart();
                });
            }
        }).start();
    }

    private void setupLineChart() {
        List<Entry> entries = new ArrayList<>();

        // Convert glycemia data to chart entries
        for (HistoriqueAdapter.GlycemieEntry entry : glycemieList) {
            try {
                float value = Float.parseFloat(entry.valeur);
                if (entry.timestamp > 0) {
                    entries.add(new Entry(entry.timestamp, value));
                    Log.d(TAG, "Added entry - Time: " + new SimpleDateFormat("dd/MM/yyyy HH:mm").format(new Date((long) entry.timestamp)) + ", Value: " + value);
                } else {
                    Log.w(TAG, "Skipping entry with invalid timestamp: " + entry.valeur + ", DateTime: " + entry.dateTime);
                }
            } catch (NumberFormatException e) {
                Log.e(TAG, "Invalid value format: " + entry.valeur + ", DateTime: " + entry.dateTime, e);
            }
        }

        if (entries.isEmpty()) {
            Log.w(TAG, "No valid entries to plot");
            lineChart.setNoDataText("Aucune donnée de glycémie valide disponible");
            lineChart.invalidate();
        } else {
            // Create a dataset
            LineDataSet dataSet = new LineDataSet(entries, "Niveaux de glycémie (mg/dL)");
            dataSet.setColor(getResources().getColor(android.R.color.holo_blue_light));
            dataSet.setValueTextColor(getResources().getColor(android.R.color.black));
            dataSet.setLineWidth(2f);
            dataSet.setCircleColor(getResources().getColor(android.R.color.holo_blue_dark));
            dataSet.setCircleRadius(4f);
            dataSet.setDrawCircleHole(true);

            // Create LineData object
            LineData lineData = new LineData(dataSet);

            // Customize the chart
            lineChart.setData(lineData);
            lineChart.getDescription().setText("Glycémie au fil du temps");
            lineChart.getDescription().setTextSize(12f);
            lineChart.setDrawGridBackground(false);
            lineChart.getXAxis().setDrawGridLines(false);
            lineChart.getAxisLeft().setDrawGridLines(true);
            lineChart.getAxisRight().setEnabled(false);

            // Format x-axis to display dates
            lineChart.getXAxis().setValueFormatter(new ValueFormatter() {
                private final SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");

                @Override
                public String getFormattedValue(float value) {
                    return sdf.format(new Date((long) value));
                }
            });

            // Refresh the chart
            lineChart.invalidate();
            Log.d(TAG, "Chart refreshed with " + entries.size() + " entries");
        }
    }
}