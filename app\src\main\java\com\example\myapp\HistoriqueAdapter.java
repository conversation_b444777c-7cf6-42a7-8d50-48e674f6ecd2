package com.example.myapp;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;

public class HistoriqueAdapter extends RecyclerView.Adapter<HistoriqueAdapter.ViewHolder> {

    private List<GlycemieEntry> glycemieList;
    private List<GlycemieEntry> filteredList;
    private RecyclerView recyclerView;

    public HistoriqueAdapter(List<GlycemieEntry> glycemieList) {
        this.glycemieList = glycemieList;
        this.filteredList = new ArrayList<>(glycemieList);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_historique, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        GlycemieEntry entry = filteredList.get(position);
        holder.textView.setText("Valeur: " + entry.valeur + " mg/dL, Date: " + entry.dateTime + ", Type: " + entry.mealTiming);
        holder.checkBox.setChecked(false); // Reset checkbox
        holder.checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Update selection state
        });
    }

    @Override
    public int getItemCount() {
        return filteredList.size();
    }

    // Filter the list based on search text (handled by activity now)
    public void filter(String searchText) {
        filteredList.clear();
        if (searchText.isEmpty()) {
            filteredList.addAll(glycemieList);
        } else {
            for (GlycemieEntry entry : glycemieList) {
                if (entry.dateTime.toLowerCase().contains(searchText.toLowerCase())) {
                    filteredList.add(entry);
                }
            }
        }
        notifyDataSetChanged();
    }

    // Get selected items for deletion
    public List<GlycemieEntry> getSelectedItems() {
        List<GlycemieEntry> selectedItems = new ArrayList<>();
        for (int i = 0; i < filteredList.size(); i++) {
            ViewHolder holder = (ViewHolder) recyclerView.findViewHolderForAdapterPosition(i);
            if (holder != null && holder.checkBox.isChecked()) {
                selectedItems.add(filteredList.get(i));
            }
        }
        return selectedItems;
    }

    // Update the list after deletion
    public void updateList(List<GlycemieEntry> newList) {
        glycemieList.clear();
        glycemieList.addAll(newList);
        filteredList.clear();
        filteredList.addAll(glycemieList);
        notifyDataSetChanged();
    }

    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        this.recyclerView = recyclerView;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        CheckBox checkBox;
        TextView textView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            checkBox = itemView.findViewById(R.id.checkbox_select);
            textView = itemView.findViewById(R.id.historique_item_text); // Fixed typo
        }
    }

    // GlycemieEntry class matching Historique data
    public static class GlycemieEntry {
        String valeur;
        String dateTime;
        String mealTiming;
        long timestamp;

        GlycemieEntry(String valeur, String dateTime, String mealTiming, long timestamp) {
            this.valeur = valeur;
            this.dateTime = dateTime;
            this.mealTiming = mealTiming;
            this.timestamp = timestamp;
        }
    }
}