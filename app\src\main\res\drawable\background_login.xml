<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- اللون الأساسي للخلفية -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#E6F0FA" /> <!-- أزرق فاتح -->
        </shape>
    </item>

    <!-- إضافة دوائر بيضاء شفافة لتأثير خفيف -->
    <item>
        <shape android:shape="oval">
            <size
                android:width="150dp"
                android:height="150dp" />
            <solid android:color="#33FFFFFF" /> <!-- أبيض شفاف -->
            <padding
                android:left="50dp"
                android:top="50dp" />
        </shape>
    </item>

    <!-- دائرة أخرى لتأثير بصري إضافي -->
    <item>
        <shape android:shape="oval">
            <size
                android:width="200dp"
                android:height="200dp" />
            <solid android:color="#22FFFFFF" /> <!-- أبيض شفاف أقل -->
            <padding
                android:right="30dp"
                android:bottom="30dp" />
        </shape>
    </item>

    <!-- رمز قطرة دم صغيرة في الزاوية -->
    <item
        android:drawable="@drawable/ic_blood_drop"
        android:gravity="bottom|end"
        android:width="50dp"
        android:height="50dp"
        android:right="20dp"
        android:bottom="20dp" />
</layer-list>