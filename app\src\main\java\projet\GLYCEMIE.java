package projet;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import android.util.Log;

import static projet.Shared.con;

/**
 * <AUTHOR>
 */
public class GLYCEMIE {
    int Id_GLYCEMIE;
    double valeur;
    String date;
    String heure;
    String before_after;
    int Id_UTILISATEUR;

    static ArrayList<GLYCEMIE> gl = new ArrayList<>();
    static ArrayList<GLYCEMIE> l = new ArrayList<>();

    // Constructor for creating a new GLYCEMIE (Id_GLYCEMIE will be auto-generated)
    public GLYCEMIE(double valeur, String date, String heure, String before_after, int Id_UTILISATEUR) {
        this.valeur = valeur;
        this.date = date;
        this.heure = heure;
        this.before_after = before_after;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    // Constructor for retrieving from database (includes Id_GLYCEMIE)
    public GLYCEMIE(int Id_GLYCEMIE, double valeur, String date, String heure, String before_after, int Id_UTILISATEUR) {
        this.Id_GLYCEMIE = Id_GLYCEMIE;
        this.valeur = valeur;
        this.date = date;
        this.heure = heure;
        this.before_after = before_after;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    @Override
    public String toString() {
        return "GLYCEMIE{" +
                "Id_GLYCEMIE=" + Id_GLYCEMIE +
                ", valeur=" + valeur +
                ", date=" + date +
                ", heure=" + heure +
                ", before_after=" + before_after +
                ", Id_UTILISATEUR=" + Id_UTILISATEUR +
                '}';
    }

    // Getters and setters
    public int getId_GLYCEMIE() {
        return Id_GLYCEMIE;
    }

    public void setId_GLYCEMIE(int Id_GLYCEMIE) {
        this.Id_GLYCEMIE = Id_GLYCEMIE;
    }

    public double getValeur() {
        return valeur;
    }

    public void setValeur(double valeur) {
        this.valeur = valeur;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getHeure() {
        return heure;
    }

    public void setHeure(String heure) {
        this.heure = heure;
    }

    public String getBefore_after() {
        return before_after;
    }

    public void setBefore_after(String before_after) {
        this.before_after = before_after;
    }

    public int getId_UTILISATEUR() {
        return Id_UTILISATEUR;
    }

    public void setId_UTILISATEUR(int Id_UTILISATEUR) {
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    public static ArrayList<GLYCEMIE> getGl() {
        return gl;
    }

    public static void setGl(ArrayList<GLYCEMIE> gl) {
        GLYCEMIE.gl = gl;
    }

    public static ArrayList<GLYCEMIE> getL() {
        return l;
    }

    public static void setL(ArrayList<GLYCEMIE> l) {
        GLYCEMIE.l = l;
    }

    // Helper method to get combined date and time
    public String getDate_heure() {
        return date + " " + heure;
    }

    public static String add(GLYCEMIE gl) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("GLYCEMIE", errorMsg);
            return errorMsg;
        }

        String sqlGlycemie = "INSERT INTO `glycemie` (`valeur`, `date`, `heure`, `before_after`, `Id_UTILISATEUR`) VALUES (?, ?, ?, ?, ?)";
        String sqlHistorique = "INSERT INTO `historique` (`valeur`, `date`, `heure`, `before_after`, `Id_UTILISATEUR`) VALUES (?, ?, ?, ?, ?)";

        try {
            // Insert into glycemie table
            PreparedStatement stmtGlycemie = con.prepareStatement(sqlGlycemie, Statement.RETURN_GENERATED_KEYS);
            stmtGlycemie.setDouble(1, gl.getValeur());
            stmtGlycemie.setString(2, gl.getDate());
            stmtGlycemie.setString(3, gl.getHeure());
            stmtGlycemie.setString(4, gl.getBefore_after());
            stmtGlycemie.setInt(5, gl.getId_UTILISATEUR());
            int rowsAffectedGlycemie = stmtGlycemie.executeUpdate();
            int glycemieId = -1;
            if (rowsAffectedGlycemie > 0) {
                ResultSet rs = stmtGlycemie.getGeneratedKeys();
                if (rs.next()) {
                    glycemieId = rs.getInt(1);
                    gl.setId_GLYCEMIE(glycemieId);
                }
            }

            // Insert into historique table
            PreparedStatement stmtHistorique = con.prepareStatement(sqlHistorique, Statement.RETURN_GENERATED_KEYS);
            stmtHistorique.setDouble(1, gl.getValeur());
            stmtHistorique.setString(2, gl.getDate());
            stmtHistorique.setString(3, gl.getHeure());
            stmtHistorique.setString(4, gl.getBefore_after());
            stmtHistorique.setInt(5, gl.getId_UTILISATEUR());
            int rowsAffectedHistorique = stmtHistorique.executeUpdate();
            int historiqueId = -1;
            if (rowsAffectedHistorique > 0) {
                ResultSet rsHistorique = stmtHistorique.getGeneratedKeys();
                if (rsHistorique.next()) {
                    historiqueId = rsHistorique.getInt(1);
                    // No need to set an ID on GLYCEMIE object for historique
                }
            }

            if (rowsAffectedGlycemie > 0 && rowsAffectedHistorique > 0) {
                return "Glycémie ajoutée avec succès dans glycemie et historique";
            } else {
                return "Échec de l'ajout dans une ou les deux tables";
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error adding glycemie or historique: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static String Delete(GLYCEMIE gl) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("GLYCEMIE", errorMsg);
            return errorMsg;
        }

        String sql = "DELETE FROM `glycemie` WHERE Id_GLYCEMIE = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, gl.getId_GLYCEMIE());
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "Glycémie supprimée avec succès" : "Glycémie non trouvée";
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error deleting glycemie: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static ArrayList<GLYCEMIE> getAllForUser(int userId) {
        ArrayList<GLYCEMIE> glycemieList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("GLYCEMIE", "Database connection failed in getAllForUser. Détails: " + Shared.getLastErrorMessage());
            return glycemieList;
        }

        String sql = "SELECT * FROM `glycemie` WHERE Id_UTILISATEUR = ? ORDER BY date DESC, heure DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, userId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                GLYCEMIE glycemie = new GLYCEMIE(
                        rs.getInt("Id_GLYCEMIE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                glycemieList.add(glycemie);
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error fetching glycemie for user: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return glycemieList;
    }

    public static ArrayList<GLYCEMIE> getAllFromHistoriqueForUser(int userId) {
        ArrayList<GLYCEMIE> glycemieList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("GLYCEMIE", "Database connection failed in getAllFromHistoriqueForUser. Détails: " + Shared.getLastErrorMessage());
            return glycemieList;
        }

        String sql = "SELECT * FROM `historique` WHERE Id_UTILISATEUR = ? ORDER BY date DESC, heure DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, userId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                GLYCEMIE glycemie = new GLYCEMIE(
                        rs.getInt("Id_HISTORIQUE"), // Use Id_HISTORIQUE from historique table
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                glycemieList.add(glycemie);
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error fetching historique for user: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return glycemieList;
    }

    public static ArrayList<GLYCEMIE> Chercher_Id_GLYCEMIE(String id) {
        ArrayList<GLYCEMIE> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("GLYCEMIE", "Database connection failed in Chercher_Id_GLYCEMIE. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `glycemie` WHERE Id_GLYCEMIE LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, id + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                GLYCEMIE glycemie = new GLYCEMIE(
                        rs.getInt("Id_GLYCEMIE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(glycemie);
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error searching by Id_GLYCEMIE: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<GLYCEMIE> Chercher_date(String date) {
        ArrayList<GLYCEMIE> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("GLYCEMIE", "Database connection failed in Chercher_date. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `glycemie` WHERE date LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, date + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                GLYCEMIE glycemie = new GLYCEMIE(
                        rs.getInt("Id_GLYCEMIE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(glycemie);
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error searching by date: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<GLYCEMIE> Chercher_heure(String heure) {
        ArrayList<GLYCEMIE> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("GLYCEMIE", "Database connection failed in Chercher_heure. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `glycemie` WHERE heure LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, heure + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                GLYCEMIE glycemie = new GLYCEMIE(
                        rs.getInt("Id_GLYCEMIE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(glycemie);
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error searching by heure: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<GLYCEMIE> Chercher_before_after(String beforeAfter) {
        ArrayList<GLYCEMIE> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("GLYCEMIE", "Database connection failed in Chercher_before_after. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `glycemie` WHERE before_after LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, beforeAfter + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                GLYCEMIE glycemie = new GLYCEMIE(
                        rs.getInt("Id_GLYCEMIE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(glycemie);
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error searching by before_after: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<GLYCEMIE> Chercher_Id_UTILISATEUR(String userId) {
        ArrayList<GLYCEMIE> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("GLYCEMIE", "Database connection failed in Chercher_Id_UTILISATEUR. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `glycemie` WHERE Id_UTILISATEUR LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, userId + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                GLYCEMIE glycemie = new GLYCEMIE(
                        rs.getInt("Id_GLYCEMIE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(glycemie);
            }
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error searching by Id_UTILISATEUR: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static String Modifier_GLYCEMIE(GLYCEMIE gl) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("GLYCEMIE", errorMsg);
            return errorMsg;
        }

        String sql = "UPDATE `glycemie` SET `valeur` = ?, `date` = ?, `heure` = ?, `before_after` = ?, `Id_UTILISATEUR` = ? WHERE `Id_GLYCEMIE` = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setDouble(1, gl.getValeur());
            stmt.setString(2, gl.getDate());
            stmt.setString(3, gl.getHeure());
            stmt.setString(4, gl.getBefore_after());
            stmt.setInt(5, gl.getId_UTILISATEUR());
            stmt.setInt(6, gl.getId_GLYCEMIE());

            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "Glycémie sauvegardée avec succès" : "Glycémie non trouvée";
        } catch (Exception e) {
            Log.e("GLYCEMIE", "Error updating glycemie: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }
}