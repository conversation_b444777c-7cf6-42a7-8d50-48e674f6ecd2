package com.example.myapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;
import projet.ALERTE;
import projet.Shared;

public class AlerteActivity extends AppCompatActivity {

    private static final String TAG = "AlerteActivity";
    private Button backButton, btnViewHistory;
    private TextView alerteMessage, alerteValue;
    private RecyclerView recyclerViewAlertes;
    private AlerteAdapter alerteAdapter;
    private ArrayList<ALERTE> alerteList;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_alerte);

        // Initialize views
        backButton = findViewById(R.id.back_button);
        btnViewHistory = findViewById(R.id.btnViewHistory);
        alerteMessage = findViewById(R.id.alerte_message);
        alerteValue = findViewById(R.id.alerte_value);
        recyclerViewAlertes = findViewById(R.id.recyclerViewAlertes);
        mainHandler = new Handler(Looper.getMainLooper());

        // Initialize alerte list
        alerteList = new ArrayList<>();

        // Setup RecyclerView
        setupRecyclerView();

        // Get glycemia value from intent if available
        double glycemiaValue = getIntent().getDoubleExtra("glycemia_value", -1);
        if (glycemiaValue != -1) {
            handleGlycemiaAlert(glycemiaValue);
        }

        // Setup button listeners
        setupButtonListeners();

        // Load alert history
        loadAlertHistory();
    }

    private void setupRecyclerView() {
        alerteAdapter = new AlerteAdapter(alerteList);
        recyclerViewAlertes.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewAlertes.setAdapter(alerteAdapter);
    }

    private void handleGlycemiaAlert(double glycemiaValue) {
        // Update UI with current alert
        String alertType = ALERTE.determineAlertType(glycemiaValue);
        String message = ALERTE.generateAlertMessage(alertType, glycemiaValue);

        alerteMessage.setText(message);
        alerteValue.setText("Valeur: " + glycemiaValue + " mg/dL");

        // Save alert to database
        saveAlertToDatabase(glycemiaValue, alertType, message);
    }

    private void saveAlertToDatabase(double glycemiaValue, String alertType, String message) {
        // Get current user ID
        int userId = Shared.utilisateur != null ? Shared.utilisateur.getId_UTILISATEUR() : -1;
        if (userId == -1) {
            // Try to get user from SharedPreferences
            SharedPreferences prefs = getSharedPreferences("UserPrefs", MODE_PRIVATE);
            String email = prefs.getString("email", "");
            String password = prefs.getString("password", "");

            if (!email.isEmpty() && !password.isEmpty()) {
                new Thread(() -> {
                    try {
                        projet.UTILISATEUR user = projet.UTILISATEUR.authentifier(email, password);
                        if (user != null) {
                            Shared.utilisateur = user;
                            saveAlert(glycemiaValue, alertType, message, user.getId_UTILISATEUR());
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error authenticating user: " + e.getMessage(), e);
                    }
                }).start();
            }
            return;
        }

        saveAlert(glycemiaValue, alertType, message, userId);
    }

    private void saveAlert(double glycemiaValue, String alertType, String message, int userId) {
        new Thread(() -> {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                String currentDateTime = sdf.format(new Date());

                String result = ALERTE.createAlertFromGlycemia(glycemiaValue, userId, currentDateTime);
                Log.d(TAG, "Alert save result: " + result);

                // Reload alert history
                mainHandler.post(this::loadAlertHistory);
            } catch (Exception e) {
                Log.e(TAG, "Error saving alert: " + e.getMessage(), e);
            }
        }).start();
    }

    private void loadAlertHistory() {
        // Get current user ID
        int userId = Shared.utilisateur != null ? Shared.utilisateur.getId_UTILISATEUR() : -1;
        if (userId == -1) {
            return;
        }

        new Thread(() -> {
            try {
                ArrayList<ALERTE> alertes = ALERTE.getAllForUser(userId);
                mainHandler.post(() -> {
                    alerteList.clear();
                    alerteList.addAll(alertes);
                    alerteAdapter.notifyDataSetChanged();
                    Log.d(TAG, "Loaded " + alertes.size() + " alertes");
                });
            } catch (Exception e) {
                Log.e(TAG, "Error loading alert history: " + e.getMessage(), e);
                mainHandler.post(() -> {
                    Toast.makeText(this, "Erreur lors du chargement de l'historique", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void setupButtonListeners() {
        // Back button listener
        backButton.setOnClickListener(v -> {
            Intent intent = new Intent(AlerteActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        });

        // View history button listener
        if (btnViewHistory != null) {
            btnViewHistory.setOnClickListener(v -> {
                // Toggle visibility of RecyclerView
                if (recyclerViewAlertes.getVisibility() == android.view.View.VISIBLE) {
                    recyclerViewAlertes.setVisibility(android.view.View.GONE);
                    btnViewHistory.setText("Voir l'historique");
                } else {
                    recyclerViewAlertes.setVisibility(android.view.View.VISIBLE);
                    btnViewHistory.setText("Masquer l'historique");
                    loadAlertHistory();
                }
            });
        }
    }
}