<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="20dp"
    android:background="@drawable/gradient_background"> <!-- خلفية متدرجة -->


    <!-- أيقونة التنبيه -->
    <ImageView
        android:id="@+id/alerte_icon"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="50dp"
        android:contentDescription="Icône Alerte"
        android:src="@drawable/zzz">

    </ImageView>



    <!-- الرسالة الحمراء الكبيرة -->
    <TextView
        android:id="@+id/alerte_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/alerte_icon"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:text="Vous êtes en danger, consultez un médecin immédiatement"
        android:textColor="#FF0000"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="16dp" />

    <!-- قيمة الجلايسيميا -->
    <TextView
        android:id="@+id/alerte_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/alerte_message"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:text="Valeur: -- mg/dL"
        android:textColor="#333333"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- أزرار التحكم -->
    <LinearLayout
        android:id="@+id/button_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/alerte_value"
        android:layout_marginTop="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/back_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="10dp"
            android:backgroundTint="#FF6200EE"
            android:text="Retour"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btnViewHistory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:backgroundTint="#4CAF50"
            android:text="Voir l'historique"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- قائمة الإنذارات -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewAlertes"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/button_layout"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:background="#FFFFFF"
        android:padding="10dp"
        android:elevation="4dp"
        android:visibility="gone" />

</RelativeLayout>