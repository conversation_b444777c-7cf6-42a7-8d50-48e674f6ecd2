<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp"
    android:background="@android:color/white"
    android:layout_marginBottom="10dp">

    <!-- Id_ALIMENTATION -->
    <TextView
        android:id="@+id/id_alimentation_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Id_ALIMENTATION: N/A"
        android:textColor="#000000"
        android:textSize="16sp" />

    <!-- description -->
    <TextView
        android:id="@+id/description_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:text="Description: N/A"
        android:textColor="#000000"
        android:textSize="16sp" />

    <!-- date_heure -->
    <TextView
        android:id="@+id/date_heure_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:text="Date et Heure: N/A"
        android:textColor="#000000"
        android:textSize="16sp" />

    <!-- impact_glycémie -->
    <TextView
        android:id="@+id/impact_glycemie_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:text="Impact sur la glycémie: N/A"
        android:textColor="#000000"
        android:textSize="16sp" />

</LinearLayout>