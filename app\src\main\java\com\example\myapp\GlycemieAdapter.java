package com.example.myapp;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import projet.GLYCEMIE;
import java.util.ArrayList;

public class GlycemieAdapter extends RecyclerView.Adapter<GlycemieAdapter.GlycemieViewHolder> {

    private ArrayList<GLYCEMIE> glycemieList;

    public GlycemieAdapter(ArrayList<GLYCEMIE> glycemieList) {
        this.glycemieList = glycemieList;
    }

    @NonNull
    @Override
    public GlycemieViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_glycemie, parent, false);
        return new GlycemieViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull GlycemieViewHolder holder, int position) {
        GLYCEMIE glycemie = glycemieList.get(position);
        holder.valueTextView.setText("Valeur: " + glycemie.getValeur() + " mg/dL");
        holder.dateTextView.setText("Date: " + glycemie.getDate());
        holder.timeTextView.setText("Heure: " + glycemie.getHeure());
        holder.mealTimingTextView.setText("Type: " + glycemie.getBefore_after());
    }

    @Override
    public int getItemCount() {
        return glycemieList.size();
    }

    public static class GlycemieViewHolder extends RecyclerView.ViewHolder {
        TextView valueTextView, dateTextView, timeTextView, mealTimingTextView;

        public GlycemieViewHolder(@NonNull View itemView) {
            super(itemView);
            valueTextView = itemView.findViewById(R.id.glycemie_value);
            dateTextView = itemView.findViewById(R.id.glycemie_date);
            timeTextView = itemView.findViewById(R.id.glycemie_time);
            mealTimingTextView = itemView.findViewById(R.id.glycemie_meal_timing);
        }
    }

    public void updateData(ArrayList<GLYCEMIE> newList) {
        this.glycemieList = newList;
        notifyDataSetChanged();
    }
}