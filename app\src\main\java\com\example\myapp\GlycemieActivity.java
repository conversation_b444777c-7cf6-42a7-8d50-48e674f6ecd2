package com.example.myapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.google.gson.Gson;
import projet.GLYCEMIE;
import projet.Shared;
import projet.UTILISATEUR;

public class GlycemieActivity extends AppCompatActivity {

    private static final String TAG = "GlycemieActivity";
    private static final String ACTION_NEW_GLYCEMIE = "com.example.myapp.NEW_GLYCEMIE";
    private EditText editValeur, editDateHeure;
    private Button btnSaveMesure, btnBack2;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_glycemie);

        // Bind UI elements
        editValeur = findViewById(R.id.editValeur);
        editDateHeure = findViewById(R.id.editDateHeure);
        btnSaveMesure = findViewById(R.id.btnSaveMesure);
        btnBack2 = findViewById(R.id.btnBack2);
        mainHandler = new Handler(Looper.getMainLooper());

        // Save button listener
        btnSaveMesure.setOnClickListener(v -> {
            String valeurStr = editValeur.getText().toString().trim();
            String dateTime = editDateHeure.getText().toString().trim();

            // Validate inputs
            if (valeurStr.isEmpty()) {
                Toast.makeText(this, "يرجى إدخال قيمة السكر", Toast.LENGTH_SHORT).show();
                return;
            }

            if (dateTime.isEmpty()) {
                Toast.makeText(this, "يرجى إدخال التاريخ والوقت", Toast.LENGTH_SHORT).show();
                return;
            }

            // Parse glycemia value to double
            double glycemieValue;
            try {
                glycemieValue = Double.parseDouble(valeurStr);
            } catch (NumberFormatException e) {
                Toast.makeText(this, "يرجى إدخال قيمة عددية صحيحة", Toast.LENGTH_SHORT).show();
                return;
            }

            // Validate dateTime format (expected: dd/MM/yyyy HH:mm)
            if (!dateTime.matches("\\d{2}/\\d{2}/\\d{4} \\d{2}:\\d{2}")) {
                Toast.makeText(this, "يرجى إدخال التاريخ والوقت بالصيغة: dd/MM/yyyy HH:mm", Toast.LENGTH_SHORT).show();
                return;
            }

            // Get the current user ID from Shared.utilisateur
            if (Shared.utilisateur == null) {
                Toast.makeText(this, "المستخدم غير مسجل الدخول", Toast.LENGTH_LONG).show();
                Intent intent = new Intent(GlycemieActivity.this, LoginActivity.class);
                startActivity(intent);
                finish();
                return;
            }
            int userId = Shared.utilisateur.getId_UTILISATEUR();

            // Split dateTime into date and time for GLYCEMIE object
            String[] dateTimeParts = dateTime.split(" ");
            String selectedDate = dateTimeParts[0]; // dd/MM/yyyy
            String selectedTime = dateTimeParts[1]; // HH:mm

            // Create GLYCEMIE object (no mealTiming since not in XML)
            GLYCEMIE glycemie = new GLYCEMIE(glycemieValue, selectedDate, selectedTime, "", userId);

            // Save to database in a background thread
            new Thread(() -> {
                String result = GLYCEMIE.add(glycemie);
                mainHandler.post(() -> {
                    Toast.makeText(GlycemieActivity.this, result, Toast.LENGTH_LONG).show();
                    if (result.contains("succès")) {
                        // Save last value to SharedPreferences for MainActivity
                        SharedPreferences prefs = getSharedPreferences("UserPrefs", MODE_PRIVATE);
                        SharedPreferences.Editor editor = prefs.edit();
                        editor.putString("last_glycemie_valeur", valeurStr); // Save as String for display
                        editor.putString("last_glycemie_heure", dateTime);
                        editor.putString("last_meal_timing", ""); // Empty since not used
                        editor.apply();

                        // Broadcast the new glycemie measurement
                        Intent broadcastIntent = new Intent(ACTION_NEW_GLYCEMIE);
                        broadcastIntent.putExtra("new_glycemie", new Gson().toJson(glycemie));
                        sendBroadcast(broadcastIntent);
                        Log.d(TAG, "Broadcast sent with new glycemie: " + glycemie.toString());

                        // Check if value is too low or too high
                        if (glycemieValue < 70 || glycemieValue > 180) {
                            Intent intent = new Intent(GlycemieActivity.this, AlerteActivity.class);
                            intent.putExtra("glycemia_value", glycemieValue);
                            startActivity(intent);
                            finish();
                        } else {
                            Intent intent = new Intent(GlycemieActivity.this, MainActivity.class);
                            startActivity(intent);
                            finish();
                        }
                    } else {
                        String errorMessage = Shared.getLastErrorMessage();
                        if (errorMessage != null && errorMessage.contains("Connection error")) {
                            Toast.makeText(GlycemieActivity.this, "خطأ في الاتصال بقاعدة البيانات: " + errorMessage, Toast.LENGTH_LONG).show();
                        }
                    }
                });
            }).start();
        });

        // Back button listener
        btnBack2.setOnClickListener(v -> {
            Intent intent = new Intent(GlycemieActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        });
    }
}