package projet;

import java.sql.Statement;
import java.util.ArrayList;
import static projet.Shared.con;

/**
 *
 * <AUTHOR>
 */
public class NOTIFICATION {
    String Id_NOTIFICATION ;
    String message;
    String type ;
    String date_heure ;
    static ArrayList<NOTIFICATION> notifecation=new ArrayList<>();
    static ArrayList<NOTIFICATION> L=new ArrayList<>();

    public NOTIFICATION( String Id_NOTIFICATION, String type, String message, String date_heure) {
        this.Id_NOTIFICATION = Id_NOTIFICATION;
        this.type = type;
        this.message = message;
        this.date_heure = date_heure;
    }

    @Override
    public String toString() {
        return "NOTIFICATION{" + "Id_NOTIFICATION=" + Id_NOTIFICATION + ", type=" + type + ", message=" + message + ", date_heure=" + date_heure + '}';
    }

    public String getId_NOTIFICATION() {
        return Id_NOTIFICATION;
    }




    public String getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public String getDate_heure() {
        return date_heure;
    }



    public static ArrayList<NOTIFICATION> getNotifecation() {
        return notifecation;
    }

    public static ArrayList<NOTIFICATION> getL() {
        return L;
    }

    public void setId_NOTIFICATION(String Id_NOTIFICATION) {
        this.Id_NOTIFICATION = Id_NOTIFICATION;
    }



    public void setType(String type) {
        this.type = type;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setDate_heure(String date_heure) {
        this.date_heure = date_heure;
    }



    public static void setNotifecation(ArrayList<NOTIFICATION> notifecation) {
        NOTIFICATION.notifecation = notifecation;
    }

    public static void setL(ArrayList<NOTIFICATION> L) {
        NOTIFICATION.L = L;
    }
    public static String add(NOTIFICATION NOT) {

        Shared.connecter();
        String req=
                "INSERT INTO `notification`(`Id_NOTIFECATION`, `message`, `type`, `date_heure`)  VALUES "
                        + "('"+NOT.getId_NOTIFICATION()+"','"+NOT.getMessage()+"','"+NOT.getType()+"','"+NOT.getDate_heure()+"')";
        System.out.print(req);
        try{

            Statement stmt=con.createStatement();
            stmt.executeUpdate(req);

        }
        catch(Exception e){
            return e.toString();}
        Shared.deconnecter();
        return "NOTIFICATION ajoute avec succees";


    }

    public static String Delete (NOTIFICATION NOT){
        String req=
                "DELETE FROM `notification` WHERE Id_NOTIFECATION='"+NOT.getId_NOTIFICATION()+"'";
        System.out.print(req);
        Shared.connecter();
        try{

            Statement stmt=con.createStatement();
            stmt.executeUpdate(req);

        }
        catch(Exception e){
            return e.toString();}
        Shared.deconnecter();
        return "NOTIFICATION supprimer avec succees";
    }


    // Add this method inside the NOTIFICATION class
    public static void displayAllNotifications() {
        Shared.connecter();
        String req = "SELECT * FROM notification";
        try {
            Statement stmt = con.createStatement();
            java.sql.ResultSet rs = stmt.executeQuery(req);
            while (rs.next()) {
                String id = rs.getString("Id_NOTIFECATION");
                String message = rs.getString("message");
                String type = rs.getString("type");
                String date_heure = rs.getString("date_heure");
                System.out.println("ID: " + id + ", Message: " + message + ", Type: " + type + ", Time: " + date_heure);
            }
        } catch (Exception e) {
            System.out.println("Error querying database: " + e.getMessage());
        }
        Shared.deconnecter();
    }



    public static ArrayList<NOTIFICATION>  Chercher_Id_NOTIFICATION(String NOT)  {

        String req="SELECT * FROM `notifIcation` WHERE  Id_NOTIFICATION like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> l=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("date_heure");


                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                l.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return l;

    }

    public static ArrayList<NOTIFICATION>  Chercher_Message(String NOT)  {

        String req="SELECT * FROM `notification` WHERE  Message like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> L=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("date_heure");


                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                L.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return L;

    }

    public static ArrayList<NOTIFICATION>  Chercher_type(String NOT)  {

        String req="SELECT * FROM `notification` WHERE  type like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> l=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("Date_Heure");

                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                L.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return l;

    }

    public static ArrayList<NOTIFICATION>  Chercher_date_heure(String NOT)  {

        String req="SELECT * FROM `notification` WHERE  date_heure like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> l=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("Date_Heure");

                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                L.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return l;

    }


    public static String Modifier_NOTIFICATION (NOTIFICATION NOT){
        String req=
                "UPDATE `notification` SET " +

                        "`Message`='" + NOT.getMessage()+ "', " +
                        "`type`='" + NOT.getType()+ "', " +
                        "`Date_Heure`='" + NOT.getDate_heure()+ "', " +
                        "WHERE `Id_NOTIFICATION`='" +NOT.getId_NOTIFICATION()+ "';";
        System.out.print(req);
        Shared.connecter();
        try{

            Statement stmt=con.createStatement();
            stmt.executeUpdate(req);

        } catch(Exception e){
            return e.toString();}
        Shared.deconnecter();
        return "NOTIFICATION sauvgarde avec succees";}




    public static void main(String[] args) {
        NOTIFICATION notification =new NOTIFICATION("fff","hhgggg","gg","khhhha");
        String msg=
                NOTIFICATION.add(notification);

 /* NOTIFICATION NOTIFICATION=new NOTIFICATION("hhh","hhh","hhhh","khhhha");
        NOTIFICATION.setId_NOTIFICATION("hhh");
        String result = Delete(NOTIFICATION);
        System.out.println(result);
  /* ArrayList<NOTIFICATION> NOTIFICATIONTROUVER = Chercher_Id_NOTIFICATION("55");

        if (NOTIFICATIONTROUVER.isEmpty()) {
            System.out.println("Aucun NOTIFICATION trouvé avec le code : " + "gggg");
        } else {
            System.out.println("alerte trouvés avec le code '" +" gggg" + "' :");
            for (NOTIFICATION NOT : NOTIFICATIONTROUVER) {
                System.out.println(NOT); */
    }
}






