package com.example.myapp;

import static android.os.Build.VERSION_CODES.R;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.GridLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

public class MainActivity extends AppCompatActivity {

    private Button btnBack, btnLogout;
    private GridLayout cardGrid;
    private TextView welcomeName, welcomeDesc;
    private static final String TAG = "MainActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // ربط الأزرار
        btnBack = findViewById(R.id.btnBack);
        btnLogout = findViewById(R.id.btnLogout);
        cardGrid = findViewById(R.id.cardGrid);
        welcomeName = findViewById(R.id.welcomeName);
        welcomeDesc = findViewById(R.id.welcomeDesc);

        // التحقق من null
        if (cardGrid == null) {
            Log.e(TAG, "cardGrid is null. Check activity_main.xml for correct ID.");
            Toast.makeText(this, "خطأ في تحميل شبكة البطاقات", Toast.LENGTH_LONG).show();
            return;
        }

        if (btnBack == null || btnLogout == null || welcomeName == null || welcomeDesc == null) {
            Log.e(TAG, "One or more UI elements are null. Check activity_main.xml for correct IDs.");
            Toast.makeText(this, "خطأ في تحميل واجهة المستخدم", Toast.LENGTH_LONG).show();
            return;
        }

        // استرجاع اسم المستخدم من Intent
        Intent intent = getIntent();
        String username = intent.getStringExtra("username");

        // تحديث النصوص بناءً على اسم المستخدم
        if (username != null && !username.isEmpty()) {
            welcomeName.setText("Bienvenue, " + username);
            welcomeDesc.setText("Gérez votre santé, " + username);
        } else {
            welcomeName.setText("Bienvenue");
            welcomeDesc.setText("Bienvenue dans votre espace santé");
        }

        // التعامل مع زر الرجوع
        btnBack.setOnClickListener(v -> finish());

        // التعامل مع زر الخروج
        btnLogout.setOnClickListener(v -> {
            Intent logoutIntent = new Intent(MainActivity.this, LoginActivity.class);
            startActivity(logoutIntent);
            finish();
        });

        // إضافة مستمعين للبطاقات
        setSingleEvent(cardGrid);
    }

    private void setSingleEvent(GridLayout gridLayout) {
        int childCount = gridLayout.getChildCount();
        Log.d(TAG, "Number of cards in GridLayout: " + childCount);

        if (childCount == 0) {
            Log.e(TAG, "No cards found in GridLayout. Check activity_main.xml for CardView elements.");
            Toast.makeText(this, "لا توجد بطاقات في الشبكة", Toast.LENGTH_LONG).show();
            return;
        }

        for (int i = 0; i < childCount; i++) {
            if (gridLayout.getChildAt(i) instanceof CardView) {
                CardView cardView = (CardView) gridLayout.getChildAt(i);
                final int index = i;

                cardView.setOnClickListener(v -> {
                    Log.d(TAG, "Card clicked: index=" + index);
                    try {
                        switch (index) {
                            case 0: // Utilisateur
                                Log.d(TAG, "Starting UtilisateurActivity");
                                Intent intent = new Intent(MainActivity.this, UtilisateurActivity.class);
                                startActivity(intent);
                                break;
                            case 1: // Historique
                                Log.d(TAG, "Starting HistoriqueActivity");
                                startActivity(new Intent(MainActivity.this, HistoriqueActivity.class));
                                break;
                            case 2: // Mesure
                                Log.d(TAG, "Starting GlycemieActivity");
                                startActivity(new Intent(MainActivity.this, GlycemieActivity.class));
                                break;
                            case 3: // Alimentation
                                Log.d(TAG, "Starting AlimentationActivity");
                                startActivity(new Intent(MainActivity.this, AlimentationActivity.class));
                                break;
                            case 4: // Conseils santé
                                Log.d(TAG, "Starting DiabetesTipsActivity");
                                startActivity(new Intent(MainActivity.this, DiabetesTipsActivity.class));
                                break;
                            case 5: // Notification
                                Log.d(TAG, "Starting NotificationActivity");
                                startActivity(new Intent(MainActivity.this, NotificationActivity.class));
                                break;
                            default:
                                Log.w(TAG, "Unknown card index: " + index);
                                Toast.makeText(MainActivity.this, "بطاقة غير معروفة", Toast.LENGTH_SHORT).show();
                                break;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error starting activity for index " + index + ": " + e.getMessage(), e);
                        Toast.makeText(MainActivity.this, "خطأ في فتح النشاط: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
            } else {
                Log.w(TAG, "Child at index " + i + " is not a CardView");
            }
        }
    }


}