<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp"
    android:background="@android:color/white"
    android:layout_marginBottom="10dp"
    android:elevation="2dp">

    <!-- <PERSON><PERSON> de la glycémie -->
    <TextView
        android:id="@+id/glycemie_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Valeur: 120 mg/dL"
        android:textColor="#000000"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- Date -->
    <TextView
        android:id="@+id/glycemie_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Date: 09/05/2025"
        android:textColor="#555555"
        android:textSize="14sp" />

    <!-- Heure -->
    <TextView
        android:id="@+id/glycemie_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Heure: 14:30"
        android:textColor="#555555"
        android:textSize="14sp" />

    <!-- Avant/Après repas -->
    <TextView
        android:id="@+id/glycemie_meal_timing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Type: Avant le repas"
        android:textColor="#555555"
        android:textSize="14sp" />

</LinearLayout>