package com.example.myapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import projet.UTILISATEUR;
import projet.Shared;

public class RegisterActivity extends AppCompatActivity {
    private EditText nomInput, infoSuppInput, ageInput, emailInput, motDePasseInput;
    private Spinner spinnerSexe, spinnerDiabetesType;
    private LinearLayout pregnancyContainer;
    private Button registerButton;
    private TextView loginText;
    private Handler mainHandler;
    private static final String TAG = "RegisterActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_register);

        // Initialize views
        nomInput = findViewById(R.id.nom_input);
        infoSuppInput = findViewById(R.id.informations_supplementaires_input);
        ageInput = findViewById(R.id.age_input);
        emailInput = findViewById(R.id.email_input);
        motDePasseInput = findViewById(R.id.mot_de_passe_input);
        spinnerSexe = findViewById(R.id.spinnerSexe);
        spinnerDiabetesType = findViewById(R.id.spinnerDiabetesType);
        pregnancyContainer = findViewById(R.id.pregnancy_container);
        registerButton = findViewById(R.id.register_button);
        loginText = findViewById(R.id.login_text);
        mainHandler = new Handler(Looper.getMainLooper());

        // Check for null views
        if (nomInput == null || infoSuppInput == null || ageInput == null || emailInput == null ||
                motDePasseInput == null || spinnerSexe == null || spinnerDiabetesType == null ||
                pregnancyContainer == null || registerButton == null || loginText == null) {
            Log.e(TAG, "One or more views are null. Check activity_register.xml for correct IDs.");
            Toast.makeText(this, "خطأ في تحميل الواجهة", Toast.LENGTH_LONG).show();
            finish();
            return;
        }

        // Setup Spinners
        ArrayAdapter<CharSequence> sexAdapter = ArrayAdapter.createFromResource(
                this, R.array.sex_options, android.R.layout.simple_spinner_item);
        sexAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerSexe.setAdapter(sexAdapter);

        ArrayAdapter<CharSequence> diabetesAdapter = ArrayAdapter.createFromResource(
                this, R.array.diabetes_type_options, android.R.layout.simple_spinner_item);
        diabetesAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerDiabetesType.setAdapter(diabetesAdapter);

        // Show/hide pregnancy container based on sex selection
        spinnerSexe.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String selectedSex = parent.getItemAtPosition(position).toString();
                pregnancyContainer.setVisibility(
                        selectedSex.equals(getString(R.string.female)) ? View.VISIBLE : View.GONE);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                pregnancyContainer.setVisibility(View.GONE);
            }
        });

        // Register button click listener
        registerButton.setOnClickListener(v -> {
            String nom = nomInput.getText().toString().trim();
            String infoSupp = infoSuppInput.getText().toString().trim();
            String age = ageInput.getText().toString().trim();
            String email = emailInput.getText().toString().trim().toLowerCase();
            String motDePasse = motDePasseInput.getText().toString().trim();
            String sexe = spinnerSexe.getSelectedItem() != null ? spinnerSexe.getSelectedItem().toString() : "";
            String typeDiabete = spinnerDiabetesType.getSelectedItem() != null ? spinnerDiabetesType.getSelectedItem().toString() : "";
            String idProfil = "1";

            // Validate inputs
            if (nom.isEmpty() || age.isEmpty() || email.isEmpty() || motDePasse.isEmpty() ||
                    sexe.isEmpty() || typeDiabete.isEmpty()) {
                Toast.makeText(RegisterActivity.this, "يرجى ملء جميع الحقول الإلزامية", Toast.LENGTH_SHORT).show();
                return;
            }

            // Validate age
            int ageValue;
            try {
                ageValue = Integer.parseInt(age);
                if (ageValue <= 0) {
                    Toast.makeText(RegisterActivity.this, "يرجى إدخال عمر صالح", Toast.LENGTH_SHORT).show();
                    return;
                }
            } catch (NumberFormatException e) {
                Toast.makeText(RegisterActivity.this, "يرجى إدخال عمر صالح", Toast.LENGTH_SHORT).show();
                return;
            }

            // Validate email
            if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                Toast.makeText(RegisterActivity.this, "يرجى إدخال بريد إلكتروني صالح", Toast.LENGTH_SHORT).show();
                return;
            }

            // Log input data for debugging
            Log.d(TAG, "Registering user: nom=" + nom + ", email=" + email + ", sexe=" + sexe + ", typeDiabete=" + typeDiabete);

            // Create UTILISATEUR object
            UTILISATEUR utilisateur = new UTILISATEUR(nom, infoSupp, age, typeDiabete, email, motDePasse, idProfil, sexe);

            // Save to database
            new Thread(() -> {
                try {
                    String result = UTILISATEUR.add(utilisateur);
                    Log.d(TAG, "UTILISATEUR.add result: " + result);
                    mainHandler.post(() -> {
                        Toast.makeText(RegisterActivity.this, result, Toast.LENGTH_LONG).show();
                        if (result != null && (result.contains("نجاح") || result.contains("succès") || result.contains("success"))) {
                            SharedPreferences prefs = getSharedPreferences("UserPrefs", MODE_PRIVATE);
                            SharedPreferences.Editor editor = prefs.edit();
                            editor.putString("email", utilisateur.getEmail());
                            editor.putString("nom", utilisateur.getNom());
                            editor.putString("type_diabete", utilisateur.getType_diabète());
                            editor.apply();

                            Log.d(TAG, "Navigating to MainActivity");
                            Intent intent = new Intent(RegisterActivity.this, MainActivity.class);
                            startActivity(intent);
                            finish();
                        } else {
                            String errorMessage = Shared.getLastErrorMessage();
                            Log.e(TAG, "Registration failed. Result: " + result + ", Error: " + (errorMessage != null ? errorMessage : "None"));
                            if (errorMessage != null && errorMessage.contains("Connection error")) {
                                Toast.makeText(RegisterActivity.this, "خطأ في الاتصال بقاعدة البيانات: " + errorMessage, Toast.LENGTH_LONG).show();
                            } else {
                                Toast.makeText(RegisterActivity.this, "فشل التسجيل: " + result, Toast.LENGTH_LONG).show();
                            }
                        }
                    });
                } catch (Exception e) {
                    Log.e(TAG, "Exception in UTILISATEUR.add: " + e.getMessage(), e);
                    mainHandler.post(() -> {
                        Toast.makeText(RegisterActivity.this, "خطأ غير متوقع: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                }
            }).start();
        });

        // Login text click listener
        loginText.setOnClickListener(v -> {
            Intent intent = new Intent(RegisterActivity.this, LoginActivity.class);
            startActivity(intent);
            finish();
        });
    }
}