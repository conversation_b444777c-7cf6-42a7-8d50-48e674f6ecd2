package com.example.myapp;

import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

public class DiabetesTipsActivity extends AppCompatActivity {

    private Button btnType1, btnType2;
    private TextView tvTips;

    // Tips for Type 1 Diabetes
    private final String type1Tips = "Conseils pour les personnes atteintes de diabète de type 1 :\n\n" +
            "✔️ Mesurez votre glycémie plusieurs fois par jour, surtout avant/après les repas et l'activité physique.\n" +
            "✔️ Respectez les doses d'insuline prescrites et apprenez à les ajuster selon votre alimentation et votre glycémie.\n" +
            "✔️ Suivez une alimentation équilibrée, riche en fibres et en glucides complexes. Évitez les sucreries et les boissons sucrées.\n" +
            "✔️ Soyez attentif aux signes d’hypoglycémie (tremblements, sueurs, vertiges) et réagissez rapidement en consommant du sucre rapide.\n" +
            "✔️ Pratiquez une activité physique régulière, mais surveillez votre glycémie avant et après.\n" +
            "✔️ Gardez toujours sur vous une carte signalant que vous êtes diabétique, en cas d'urgence.\n" +
            "✔️ Utilisez une application pour suivre votre glycémie et vos doses d’insuline.\n" +
            "✔️ Faites un suivi médical régulier et un test HbA1c tous les 3 mois pour évaluer votre contrôle glycémique.";

    // Tips for Type 2 Diabetes
    private final String type2Tips = "Conseils pour les personnes atteintes de diabète de type 2 :\n\n" +
            "✔️ Adoptez une alimentation pauvre en sucres et en graisses, riche en légumes, céréales complètes et protéines légères.\n" +
            "✔️ Faites au moins 30 minutes d’activité physique par jour (marche, vélo…).\n" +
            "✔️ Perdez du poids si vous êtes en surpoids, cela améliore la sensibilité à l’insuline.\n" +
            "✔️ Surveillez régulièrement votre glycémie, surtout si vous prenez des médicaments ou de l’insuline.\n" +
            "✔️ Buvez suffisamment d’eau chaque jour.\n" +
            "✔️ Évitez totalement le tabac et l’alcool.\n" +
            "✔️ Prenez vos médicaments comme prescrits, sans interruption.\n" +
            "✔️ Consultez régulièrement votre médecin pour faire des bilans : HbA1c, cholestérol, reins, yeux et pieds.\n" +
            "✔️ Apprenez à gérer le stress, car il peut affecter votre taux de sucre.";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_diabets_tipaes_activity);

        // Initialize views
        btnType1 = findViewById(R.id.btnType1);
        btnType2 = findViewById(R.id.btnType2);
        tvTips = findViewById(R.id.tvTips);

        // Set click listener for Type 1 button
        btnType1.setOnClickListener(v -> tvTips.setText(type1Tips));

        // Set click listener for Type 2 button
        btnType2.setOnClickListener(v -> tvTips.setText(type2Tips));
    }
}