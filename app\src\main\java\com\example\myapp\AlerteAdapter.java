package com.example.myapp;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import projet.ALERTE;

public class AlerteAdapter extends RecyclerView.Adapter<AlerteAdapter.AlerteViewHolder> {

    private ArrayList<ALERTE> alerteList;

    public AlerteAdapter(ArrayList<ALERTE> alerteList) {
        this.alerteList = alerteList;
    }

    @NonNull
    @Override
    public AlerteViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_alerte, parent, false);
        return new AlerteViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AlerteViewHolder holder, int position) {
        ALERTE alerte = alerteList.get(position);
        holder.bind(alerte);
    }

    @Override
    public int getItemCount() {
        return alerteList.size();
    }

    public static class AlerteViewHolder extends RecyclerView.ViewHolder {
        private TextView textMessage, textType, textValue, textDateTime;
        private View alerteIndicator;

        public AlerteViewHolder(@NonNull View itemView) {
            super(itemView);
            textMessage = itemView.findViewById(R.id.textMessage);
            textType = itemView.findViewById(R.id.textType);
            textValue = itemView.findViewById(R.id.textValue);
            textDateTime = itemView.findViewById(R.id.textDateTime);
            alerteIndicator = itemView.findViewById(R.id.alerteIndicator);
        }

        public void bind(ALERTE alerte) {
            textMessage.setText(alerte.getMessage());
            textType.setText(alerte.getType_alerte());
            textValue.setText(String.format("%.1f mg/dL", alerte.getValeur_glycemie()));
            textDateTime.setText(alerte.getDate_heure());

            // Set color based on alert type
            switch (alerte.getType_alerte()) {
                case "HYPOGLYCEMIE":
                    alerteIndicator.setBackgroundColor(Color.parseColor("#FF5722")); // Red
                    textType.setTextColor(Color.parseColor("#FF5722"));
                    break;
                case "HYPERGLYCEMIE":
                    alerteIndicator.setBackgroundColor(Color.parseColor("#FF9800")); // Orange
                    textType.setTextColor(Color.parseColor("#FF9800"));
                    break;
                default:
                    alerteIndicator.setBackgroundColor(Color.parseColor("#4CAF50")); // Green
                    textType.setTextColor(Color.parseColor("#4CAF50"));
                    break;
            }
        }
    }
}
