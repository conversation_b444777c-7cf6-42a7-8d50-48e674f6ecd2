<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    android:background="@drawable/gradient_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:layout_margin="12dp"
        android:layoutDirection="rtl"
        android:elevation="8dp"
        android:clipToPadding="false"
        android:background="@drawable/edittext_background">

        <!-- التاريخ والوقت -->
        <EditText
            android:id="@+id/editDateHeure"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="18dp"
            android:hint="📅 Date et Heure de la mesure"
            android:padding="16dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textDirection="rtl"
            android:background="@drawable/rounded_edittext_background"
            android:inputType="datetime" />

        <!-- قيمة السكر -->
        <EditText
            android:id="@+id/editValeur"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="18dp"
            android:hint="💉 Valeur du sucre (mg/dL)"
            android:inputType="numberDecimal"
            android:padding="16dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textDirection="rtl"
            android:background="@drawable/rounded_edittext_background" />

        <Button
            android:id="@+id/btnSaveMesure"
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rounded_button_green"
            android:text="✔️ Enregistrer la mesure"
            android:textColor="@color/black"
            android:textSize="17sp" />

        <!-- زر الرجوع -->
        <Button
            android:id="@+id/btnBack2"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="⬅️ Retour"
            android:textSize="17sp"
            android:textColor="@color/black"
            android:background="@drawable/rounded_button_red"
            android:layout_marginBottom="16dp" />

    </LinearLayout>
</ScrollView>