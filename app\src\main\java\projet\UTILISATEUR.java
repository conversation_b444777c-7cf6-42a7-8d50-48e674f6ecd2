package projet;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import android.util.Log;

import static projet.Shared.con;

public class UTILISATEUR {
    int Id_UTILISATEUR;
    String nom;
    String informations_supplementaires;
    String âge;
    String type_diabète;
    String email;
    String mot_de_passe;
    String id_profil;
    String sexe;
    static ArrayList<UTILISATEUR> util = new ArrayList<>();
    static ArrayList<UTILISATEUR> l = new ArrayList<>();

    // Constructor without Id_UTILISATEUR (auto-generated by DB)
    public UTILISATEUR(String nom, String informations_supplementaires, String âge, String type_diabète,
                       String email, String mot_de_passe, String id_profil, String sexe) {
        this.nom = nom;
        this.informations_supplementaires = informations_supplementaires;
        this.âge = âge;
        this.type_diabète = type_diabète;
        this.email = email;
        this.mot_de_passe = mot_de_passe;
        this.id_profil = id_profil;
        this.sexe = sexe;
    }

    // Getters and setters
    public int getId_UTILISATEUR() {
        return Id_UTILISATEUR;
    }

    public void setId_UTILISATEUR(int Id_UTILISATEUR) {
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getInformations_supplementaires() {
        return informations_supplementaires;
    }

    public void setInformations_supplementaires(String informations_supplementaires) {
        this.informations_supplementaires = informations_supplementaires;
    }

    public String getÂge() {
        return âge;
    }

    public void setÂge(String âge) {
        this.âge = âge;
    }

    public String getType_diabète() {
        return type_diabète;
    }

    public void setType_diabète(String type_diabète) {
        this.type_diabète = type_diabète;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMot_de_passe() {
        return mot_de_passe;
    }

    public void setMot_de_passe(String mot_de_passe) {
        this.mot_de_passe = mot_de_passe;
    }

    public String getId_profil() {
        return id_profil;
    }

    public void setId_profil(String id_profil) {
        this.id_profil = id_profil;
    }

    public String getSexe() {
        return sexe;
    }

    public void setSexe(String sexe) {
        this.sexe = sexe;
    }

    public static ArrayList<UTILISATEUR> getUtil() {
        return util;
    }

    public static void setUtil(ArrayList<UTILISATEUR> util) {
        UTILISATEUR.util = util;
    }

    public static ArrayList<UTILISATEUR> getL() {
        return l;
    }

    public static void setL(ArrayList<UTILISATEUR> l) {
        UTILISATEUR.l = l;
    }

    @Override
    public String toString() {
        return "UTILISATEUR{" +
                "Id_UTILISATEUR=" + Id_UTILISATEUR +
                ", nom=" + nom +
                ", informations_supplementaires=" + informations_supplementaires +
                ", âge=" + âge +
                ", type_diabète=" + type_diabète +
                ", email=" + email +
                ", mot_de_passe=" + mot_de_passe +
                ", id_profil=" + id_profil +
                ", sexe=" + sexe + '}';
    }

    public static String add(UTILISATEUR util) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("UTILISATEUR", errorMsg);
            return errorMsg;
        }

        String sql = "INSERT INTO `utilisateur` (`nom`, `informations_supplementaires`, `âge`, `type_diabète`, `email`, `mot_de_passe`, `id_profil`, `sexe`) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            PreparedStatement stmt = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, util.getNom());
            stmt.setString(2, util.getInformations_supplementaires());
            stmt.setString(3, util.getÂge());
            stmt.setString(4, util.getType_diabète());
            stmt.setString(5, util.getEmail());
            stmt.setString(6, util.getMot_de_passe());
            stmt.setString(7, util.getId_profil());
            stmt.setString(8, util.getSexe());

            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    util.setId_UTILISATEUR(rs.getInt(1));
                }
                return "UTILISATEUR ajouté avec succès";
            } else {
                return "Échec de l'ajout de l'UTILISATEUR";
            }
        } catch (Exception e) {
            Log.e("UTILISATEUR", "Error adding user: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static String Delete(UTILISATEUR util) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("UTILISATEUR", errorMsg);
            return errorMsg;
        }

        String sql = "DELETE FROM `utilisateur` WHERE Id_UTILISATEUR = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, util.getId_UTILISATEUR());
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "Utilisateur supprimé avec succès" : "Utilisateur non trouvé";
        } catch (Exception e) {
            Log.e("UTILISATEUR", "Error deleting user: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static UTILISATEUR authentifier(String email, String mot_de_passe) {
        Shared.connecter();
        if (con == null) {
            Log.e("UTILISATEUR", "Database connection failed in authentifier method. Détails: " + Shared.getLastErrorMessage());
            return null;
        }

        // Trim inputs to remove any leading/trailing spaces
        email = email.trim();
        mot_de_passe = mot_de_passe.trim();

        Log.d("UTILISATEUR", "Authenticating user with email: " + email + ", password: " + mot_de_passe);
        String sql = "SELECT * FROM utilisateur WHERE LOWER(email) = LOWER(?) AND LOWER(mot_de_passe) = LOWER(?)";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, email.toLowerCase());
            stmt.setString(2, mot_de_passe.toLowerCase());
            ResultSet rs = stmt.executeQuery();
            Log.d("UTILISATEUR", "Query executed, checking results...");

            if (rs.next()) {
                Log.d("UTILISATEUR", "User found in database");
                UTILISATEUR u = new UTILISATEUR(
                        rs.getString("nom"),
                        rs.getString("informations_supplementaires"),
                        rs.getString("âge"),
                        rs.getString("type_diabète"),
                        rs.getString("email"),
                        rs.getString("mot_de_passe"),
                        rs.getString("id_profil"),
                        rs.getString("sexe")
                );
                u.setId_UTILISATEUR(rs.getInt("Id_UTILISATEUR"));
                Log.d("UTILISATEUR", "Returning user: " + u.toString());
                return u;
            } else {
                Log.d("UTILISATEUR", "No user found with given credentials");
                PreparedStatement debugStmt = con.prepareStatement("SELECT * FROM utilisateur WHERE LOWER(email) = LOWER(?)");
                debugStmt.setString(1, email.toLowerCase());
                ResultSet debugRs = debugStmt.executeQuery();
                if (debugRs.next()) {
                    Log.d("UTILISATEUR", "Email exists, but password does not match. Stored password: " + debugRs.getString("mot_de_passe"));
                } else {
                    Log.d("UTILISATEUR", "Email not found in database");
                }
                return null;
            }
        } catch (Exception e) {
            Log.e("UTILISATEUR", "Authentication error: " + e.getMessage(), e);
            return null;
        } finally {
            Shared.deconnecter();
        }
    }

    public static String Modifier_UTILISATEUR(UTILISATEUR util) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("UTILISATEUR", errorMsg);
            return errorMsg;
        }

        String sql = "UPDATE `utilisateur` SET `nom` = ?, `informations_supplementaires` = ?, `âge` = ?, " +
                "`type_diabète` = ?, `email` = ?, `mot_de_passe` = ?, `id_profil` = ?, `sexe` = ? " +
                "WHERE `Id_UTILISATEUR` = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, util.getNom());
            stmt.setString(2, util.getInformations_supplementaires());
            stmt.setString(3, util.getÂge());
            stmt.setString(4, util.getType_diabète());
            stmt.setString(5, util.getEmail());
            stmt.setString(6, util.getMot_de_passe());
            stmt.setString(7, util.getId_profil());
            stmt.setString(8, util.getSexe());
            stmt.setInt(9, util.getId_UTILISATEUR());

            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "UTILISATEUR sauvegardé avec succès" : "Utilisateur non trouvé";
        } catch (Exception e) {
            Log.e("UTILISATEUR", "Error updating user: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }
}