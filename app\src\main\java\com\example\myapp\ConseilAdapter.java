package com.example.myapp;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import projet.CONSEIL;

public class ConseilAdapter extends RecyclerView.Adapter<ConseilAdapter.ConseilViewHolder> {

    private ArrayList<CONSEIL> conseilList;

    public ConseilAdapter(ArrayList<CONSEIL> conseilList) {
        this.conseilList = conseilList;
    }

    @NonNull
    @Override
    public ConseilViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_conseil, parent, false);
        return new ConseilViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ConseilViewHolder holder, int position) {
        CONSEIL conseil = conseilList.get(position);
        holder.bind(conseil);
    }

    @Override
    public int getItemCount() {
        return conseilList.size();
    }

    public static class ConseilViewHolder extends RecyclerView.ViewHolder {
        private TextView textTitre, textContenu, textType, textCategorie, textDate;
        private View categoryIndicator;

        public ConseilViewHolder(@NonNull View itemView) {
            super(itemView);
            textTitre = itemView.findViewById(R.id.textTitre);
            textContenu = itemView.findViewById(R.id.textContenu);
            textType = itemView.findViewById(R.id.textType);
            textCategorie = itemView.findViewById(R.id.textCategorie);
            textDate = itemView.findViewById(R.id.textDate);
            categoryIndicator = itemView.findViewById(R.id.categoryIndicator);
        }

        public void bind(CONSEIL conseil) {
            textTitre.setText(conseil.getTitre());
            textContenu.setText(conseil.getContenu());
            textType.setText(conseil.getType_diabete());
            textCategorie.setText(conseil.getCategorie());
            textDate.setText(conseil.getDate_creation());

            // Set color based on category
            switch (conseil.getCategorie()) {
                case "Alimentation":
                    categoryIndicator.setBackgroundColor(Color.parseColor("#4CAF50")); // Green
                    textCategorie.setTextColor(Color.parseColor("#4CAF50"));
                    break;
                case "Exercice":
                    categoryIndicator.setBackgroundColor(Color.parseColor("#2196F3")); // Blue
                    textCategorie.setTextColor(Color.parseColor("#2196F3"));
                    break;
                case "Medicament":
                    categoryIndicator.setBackgroundColor(Color.parseColor("#FF9800")); // Orange
                    textCategorie.setTextColor(Color.parseColor("#FF9800"));
                    break;
                default:
                    categoryIndicator.setBackgroundColor(Color.parseColor("#9C27B0")); // Purple
                    textCategorie.setTextColor(Color.parseColor("#9C27B0"));
                    break;
            }
        }
    }
}
