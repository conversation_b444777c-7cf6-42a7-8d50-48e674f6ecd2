<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Alert Indicator -->
        <View
            android:id="@+id/alerteIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="#FF5722"
            android:layout_marginEnd="12dp" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Message d'alerte"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/textType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="HYPOGLYCEMIE"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="#FF5722" />

                <TextView
                    android:id="@+id/textValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="65.0 mg/dL"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="#333333" />

            </LinearLayout>

            <TextView
                android:id="@+id/textDateTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2024-01-01 12:00:00"
                android:textSize="12sp"
                android:textColor="#999999"
                android:gravity="end" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
