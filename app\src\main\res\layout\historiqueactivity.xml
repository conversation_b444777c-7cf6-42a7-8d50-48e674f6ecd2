<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:padding="16dp">

    <!-- Icon (Placeholder for "hist" or app logo) -->

    <!-- Title -->

    <!-- مدخل التاريخ والوقت -->
    <EditText
        android:id="@+id/etDateTime"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="18dp"
        android:hint="أدخل التاريخ والوقت"
        android:padding="16dp"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textDirection="rtl"
        android:background="@drawable/rounded_edittext_background"
        android:inputType="datetime"
        tools:ignore="MissingConstraints" />

    <androidx.cardview.widget.CardView
        android:id="@+id/search_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#80FFFFFF"
        android:layout_marginTop="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="MissingConstraints">

        <EditText
            android:id="@+id/search_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/search_hint"
            android:inputType="text"
            android:autofillHints="dateTime"
            android:padding="12dp"
            android:background="@null"
            android:textColor="#000000"
            android:textColorHint="#AAAAAA"
            android:layout_margin="8dp" />

    </androidx.cardview.widget.CardView>

    <!-- Search and Graph Buttons -->
    <LinearLayout
        android:id="@+id/button_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toBottomOf="@id/search_card"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/graph_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="#61636B"
            android:paddingStart="7dp"
            android:paddingEnd="16dp"
            android:text="Voir Graphique"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:cornerRadius="8dp" />

    </LinearLayout>

    <!-- RecyclerView inside CardView -->
    <androidx.cardview.widget.CardView
        android:id="@+id/recycler_card"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#80FFFFFF"
        android:layout_marginTop="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/button_layout"
        app:layout_constraintBottom_toTopOf="@id/delete_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/glycemie_recycler_view"
            android:layout_width="352dp"
            android:layout_height="479dp"
            android:contentDescription="@string/recycler_view_description"
            android:padding="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_historique" />

    </androidx.cardview.widget.CardView>

    <!-- Delete Button -->
    <Button
        android:id="@+id/delete_button"
        android:layout_width="144dp"
        android:layout_height="43dp"
        android:backgroundTint="#03A9F4"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:text="@string/delete_button"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.034"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/search_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:backgroundTint="#64656A"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:text="@string/search_button"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        tools:ignore="MissingConstraints"
        tools:layout_editor_absoluteX="249dp"
        tools:layout_editor_absoluteY="656dp" />

</androidx.constraintlayout.widget.ConstraintLayout>