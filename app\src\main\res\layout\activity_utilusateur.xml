<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvNom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Nom: N/A"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/tvPrenom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Prénom: N/A"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/age_input"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Âge: N/A"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/tvSexe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Sexe: N/A"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/tvDiabetesType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Type de diabète: N/A"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/tvEmail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Email: N/A"
        android:textSize="18sp" />

    <Button
        android:id="@+id/btnBack5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Retour" />

</LinearLayout>