-- ========================================
-- سكريبت إصلاح قاعدة البيانات الكامل
-- ========================================

-- 1. إصلاح جدول alerte
-- حذف الجدول القديم وإنشاء جدول جديد صحيح
DROP TABLE IF EXISTS `alerte`;
CREATE TABLE `alerte` (
  `Id_ALERTE` INT AUTO_INCREMENT PRIMARY KEY,
  `valeur_glycemie` DOUBLE NOT NULL,
  `type_alerte` VARCHAR(50) NOT NULL,
  `message` TEXT NOT NULL,
  `date_heure` DATETIME NOT NULL,
  `Id_UTILISATEUR` INT NOT NULL,
  FOREIGN KEY (`Id_UTILISATEUR`) REFERENCES `utilisateur`(`Id_UTILISATEUR`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 2. إصلاح جدول notification
-- حذف الجدول القديم وإنشاء جدول جديد صحيح
DROP TABLE IF EXISTS `notification`;
DROP TABLE IF EXISTS `reçoit`; -- حذف الجدول المرتبط أيضاً
CREATE TABLE `notification` (
  `Id_NOTIFICATION` INT AUTO_INCREMENT PRIMARY KEY,
  `message` TEXT NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `date_heure` DATETIME NOT NULL,
  `Id_UTILISATEUR` INT NOT NULL,
  FOREIGN KEY (`Id_UTILISATEUR`) REFERENCES `utilisateur`(`Id_UTILISATEUR`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. إصلاح جدول alimentation
-- حفظ البيانات الحالية مؤقتاً
CREATE TEMPORARY TABLE temp_alimentation AS
SELECT `COL 1` as nom, `COL 2` as calories, `COL 3` as glucides,
       `COL 4` as sucres, `COL 5` as proteines, `COL 6` as lipides
FROM `alimentation`
WHERE `COL 1` != 'Nourriture'; -- تجاهل السطر الأول (العناوين)

-- حذف الجدول القديم وإنشاء جدول جديد
DROP TABLE IF EXISTS `alimentation`;
CREATE TABLE `alimentation` (
  `Id_ALIMENTATION` INT AUTO_INCREMENT PRIMARY KEY,
  `nom` VARCHAR(100) NOT NULL,
  `calories` DOUBLE NOT NULL,
  `glucides` DOUBLE NOT NULL,
  `sucres` DOUBLE NOT NULL,
  `proteines` DOUBLE NOT NULL,
  `lipides` DOUBLE NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إعادة إدراج البيانات مع تحويل النصوص إلى أرقام
INSERT INTO `alimentation` (`nom`, `calories`, `glucides`, `sucres`, `proteines`, `lipides`)
SELECT
  nom,
  CAST(REPLACE(calories, ',', '.') AS DECIMAL(10,2)),
  CAST(REPLACE(glucides, ',', '.') AS DECIMAL(10,2)),
  CAST(REPLACE(sucres, ',', '.') AS DECIMAL(10,2)),
  CAST(REPLACE(proteines, ',', '.') AS DECIMAL(10,2)),
  CAST(REPLACE(lipides, ',', '.') AS DECIMAL(10,2))
FROM temp_alimentation
WHERE calories REGEXP '^[0-9.,]+$'
  AND glucides REGEXP '^[0-9.,]+$'
  AND sucres REGEXP '^[0-9.,]+$'
  AND proteines REGEXP '^[0-9.,]+$'
  AND lipides REGEXP '^[0-9.,]+$';

-- حذف الجدول المؤقت
DROP TEMPORARY TABLE temp_alimentation;

-- 4. إنشاء جدول conseil
CREATE TABLE `conseil` (
  `Id_CONSEIL` INT AUTO_INCREMENT PRIMARY KEY,
  `titre` VARCHAR(255) NOT NULL,
  `contenu` TEXT NOT NULL,
  `type_diabete` VARCHAR(50) NOT NULL,
  `categorie` VARCHAR(50) NOT NULL,
  `date_creation` DATETIME NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 5. إضافة FOREIGN KEY لجدول glycemie
ALTER TABLE `glycemie`
ADD CONSTRAINT `glycemie_ibfk_1`
FOREIGN KEY (`Id_UTILISATEUR`) REFERENCES `utilisateur`(`Id_UTILISATEUR`) ON DELETE CASCADE;

-- 6. تحسين جدول glycemie - تغيير نوع البيانات
ALTER TABLE `glycemie`
MODIFY `valeur` DOUBLE NOT NULL;

-- 7. إضافة فهارس لتحسين الأداء
CREATE INDEX idx_glycemie_user_date ON `glycemie`(`Id_UTILISATEUR`, `date`);
CREATE INDEX idx_historique_user_date ON `historique`(`Id_UTILISATEUR`, `date`);
CREATE INDEX idx_alerte_user_date ON `alerte`(`Id_UTILISATEUR`, `date_heure`);
CREATE INDEX idx_notification_user_date ON `notification`(`Id_UTILISATEUR`, `date_heure`);

-- 8. إدراج البيانات الأولية للنصائح
INSERT INTO `conseil` (`titre`, `contenu`, `type_diabete`, `categorie`, `date_creation`) VALUES

-- نصائح للنوع الأول
('مراقبة الجلايسيميا', 'قم بقياس مستوى السكر في الدم عدة مرات يومياً، خاصة قبل وبعد الوجبات وقبل النوم.', 'Type 1', 'General', NOW()),
('إدارة الأنسولين', 'احرص على أخذ جرعات الأنسولين في المواعيد المحددة وتعلم كيفية تعديل الجرعة حسب مستوى السكر والطعام.', 'Type 1', 'Medicament', NOW()),
('التعامل مع نقص السكر', 'احمل معك دائماً مصدر سكر سريع (عصير، حلوى) للتعامل مع نوبات نقص السكر.', 'Type 1', 'General', NOW()),
('النشاط البدني', 'مارس الرياضة بانتظام مع مراقبة مستوى السكر قبل وبعد التمرين.', 'Type 1', 'Exercice', NOW()),
('التغذية المتوازنة', 'تناول وجبات منتظمة وتعلم حساب الكربوهيدرات لتعديل جرعة الأنسولين.', 'Type 1', 'Alimentation', NOW()),

-- نصائح للنوع الثاني
('النظام الغذائي الصحي', 'اتبع نظاماً غذائياً قليل السكريات والدهون، غني بالخضروات والحبوب الكاملة.', 'Type 2', 'Alimentation', NOW()),
('إنقاص الوزن', 'حافظ على وزن صحي، فإنقاص الوزن يحسن من حساسية الجسم للأنسولين.', 'Type 2', 'General', NOW()),
('التمارين اليومية', 'مارس 30 دقيقة من النشاط البدني يومياً مثل المشي أو السباحة.', 'Type 2', 'Exercice', NOW()),
('تناول الأدوية', 'التزم بتناول الأدوية الموصوفة في مواعيدها دون انقطاع.', 'Type 2', 'Medicament', NOW()),
('مراقبة الضغط والكوليسترول', 'راقب ضغط الدم ومستوى الكوليسترول بانتظام.', 'Type 2', 'General', NOW()),

-- نصائح عامة
('شرب الماء', 'اشرب كمية كافية من الماء يومياً وتجنب المشروبات السكرية.', 'General', 'General', NOW()),
('الفحوصات الدورية', 'قم بإجراء فحص HbA1c كل 3 أشهر لمراقبة مستوى السكر على المدى الطويل.', 'General', 'General', NOW()),
('العناية بالقدمين', 'افحص قدميك يومياً وحافظ على نظافتهما وجفافهما.', 'General', 'General', NOW()),
('إدارة التوتر', 'تعلم تقنيات إدارة التوتر والاسترخاء، فالتوتر يؤثر على مستوى السكر.', 'General', 'General', NOW()),
('النوم الكافي', 'احرص على النوم 7-8 ساعات يومياً، فقلة النوم تؤثر على مستوى السكر.', 'General', 'General', NOW()),

-- نصائح التغذية
('تجنب السكريات البسيطة', 'تجنب الحلويات والمشروبات السكرية والأطعمة المصنعة.', 'General', 'Alimentation', NOW()),
('تناول الألياف', 'أكثر من تناول الأطعمة الغنية بالألياف مثل الخضروات والفواكه والحبوب الكاملة.', 'General', 'Alimentation', NOW()),
('وجبات صغيرة متكررة', 'تناول وجبات صغيرة ومتكررة بدلاً من وجبات كبيرة قليلة.', 'General', 'Alimentation', NOW()),

-- نصائح الرياضة
('البدء التدريجي', 'ابدأ بتمارين خفيفة وزد الشدة تدريجياً حسب قدرتك.', 'General', 'Exercice', NOW()),
('تمارين المقاومة', 'أضف تمارين المقاومة إلى روتينك الرياضي لتحسين حساسية الأنسولين.', 'General', 'Exercice', NOW());
