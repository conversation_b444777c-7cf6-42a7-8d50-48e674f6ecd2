package com.example.myapp;

import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import projet.Alimentation;
import java.util.Locale;

public class AlimentationActivity extends AppCompatActivity {
    private EditText etMealName;
    private TextView tvMealInfo;
    private Button btnBack1;
    private Handler mainHandler;
    private static final String TAG = "AlimentationActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Set Arabic locale
        Locale locale = new Locale("ar");
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());

        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_alimentation);

        // Initialize views
        etMealName = findViewById(R.id.etMealName);
        tvMealInfo = findViewById(R.id.tvMealInfo);
        btnBack1 = findViewById(R.id.btnBack1);
        mainHandler = new Handler(Looper.getMainLooper());

        // Check for null views
        if (etMealName == null || tvMealInfo == null || btnBack1 == null) {
            Log.e(TAG, "واحد أو أكثر من العناصر غير موجود. تأكد من activity_alimentation.xml");
            Toast.makeText(this, "خطأ في تحميل الواجهة", Toast.LENGTH_LONG).show();
            finish();
            return;
        }

        // Set initial text for tvMealInfo
        tvMealInfo.setText("معلومات الوجبة ستظهر هنا");

        // Add TextWatcher for real-time search
        etMealName.addTextChangedListener(new TextWatcher() {
            private Runnable searchRunnable;

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                if (searchRunnable != null) {
                    mainHandler.removeCallbacks(searchRunnable);
                }
                searchRunnable = () -> {
                    String mealName = s.toString().trim();
                    if (mealName.isEmpty()) {
                        tvMealInfo.setText("معلومات الوجبة ستظهر هنا");
                    } else {
                        searchFood(mealName);
                    }
                };
                mainHandler.postDelayed(searchRunnable, 500); // 500ms debounce
            }
        });

        // Back button to return to MainActivity
        btnBack1.setOnClickListener(v -> {
            Intent intent = new Intent(AlimentationActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        });
    }

    private void searchFood(String foodName) {
        new Thread(() -> {
            try {
                Alimentation food = Alimentation.searchByNom(foodName);
                mainHandler.post(() -> {
                    if (food != null) {
                        tvMealInfo.setText(food.toString());
                    } else {
                        tvMealInfo.setText("لم يتم العثور على الوجبة: " + foodName);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Error searching food: " + e.getMessage());
                mainHandler.post(() -> {
                    Toast.makeText(AlimentationActivity.this, "خطأ في استرجاع المعلومات: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    tvMealInfo.setText("معلومات الوجبة ستظهر هنا");
                });
            }
        }).start();
    }
}