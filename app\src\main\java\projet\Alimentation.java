/*
 * Aligned with AlimentationActivity and activity_alimentation.xml for real-time food search.
 * Interacts with MySQL table 'alimentation' (Id_ALIMENTATION, nom, calories, glucides, sucres, proteines, lipides).
 */
package projet;

import android.util.Log;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import static projet.Shared.con;

/**
 * Represents a food item with nutritional information.
 */
public class Alimentation {
    private int Id_ALIMENTATION;
    private String nom;
    private double calories;
    private double glucides;
    private double sucres;
    private double proteines;
    private double lipides;
    private static final String TAG = "Alimentation";

    // Constructors
    public Alimentation(String nom) {
        this.nom = nom;
    }

    public Alimentation(String nom, double calories, double glucides, double sucres, double proteines, double lipides) {
        this.nom = nom;
        this.calories = calories;
        this.glucides = glucides;
        this.sucres = sucres;
        this.proteines = proteines;
        this.lipides = lipides;
    }

    public Alimentation(int Id_ALIMENTATION, String nom, double calories, double glucides, double sucres, double proteines, double lipides) {
        this.Id_ALIMENTATION = Id_ALIMENTATION;
        this.nom = nom;
        this.calories = calories;
        this.glucides = glucides;
        this.sucres = sucres;
        this.proteines = proteines;
        this.lipides = lipides;
    }

    // Getters and Setters
    public int getId_ALIMENTATION() {
        return Id_ALIMENTATION;
    }

    public void setId_ALIMENTATION(int Id_ALIMENTATION) {
        this.Id_ALIMENTATION = Id_ALIMENTATION;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public double getCalories() {
        return calories;
    }

    public void setCalories(double calories) {
        this.calories = calories;
    }

    public double getGlucides() {
        return glucides;
    }

    public void setGlucides(double glucides) {
        this.glucides = glucides;
    }

    public double getSucres() {
        return sucres;
    }

    public void setSucres(double sucres) {
        this.sucres = sucres;
    }

    public double getProteines() {
        return proteines;
    }

    public void setProteines(double proteines) {
        this.proteines = proteines;
    }

    public double getLipides() {
        return lipides;
    }

    public void setLipides(double lipides) {
        this.lipides = lipides;
    }

    @Override
    public String toString() {
        return String.format(
                "Aliment : %s\nCalories : %.1f kcal\nGlucides : %.1f g\nSucres : %.1f g\nProtéines : %.1f g\nLipides : %.1f g",
                nom, calories, glucides, sucres, proteines, lipides);
    }

    // Add a food item
    public static String add(Alimentation aliment) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            System.err.println(errorMsg);
            return errorMsg;
        }

        String sql = "INSERT INTO `alimentation` (`nom`, `calories`, `glucides`, `sucres`, `proteines`, `lipides`) VALUES (?, ?, ?, ?, ?, ?)";
        try {
            PreparedStatement stmt = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, aliment.getNom());
            stmt.setDouble(2, aliment.getCalories());
            stmt.setDouble(3, aliment.getGlucides());
            stmt.setDouble(4, aliment.getSucres());
            stmt.setDouble(5, aliment.getProteines());
            stmt.setDouble(6, aliment.getLipides());

            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    aliment.setId_ALIMENTATION(rs.getInt(1));
                }
                return "Aliment ajouté avec succès";
            } else {
                return "Échec de l'ajout de l'aliment";
            }
        } catch (Exception e) {
            System.err.println("Error adding aliment: " + e.getMessage());
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    // Search for a food by name (exact or partial match)
    public static Alimentation searchByNom(String nom) {
        Shared.connecter();
        if (con == null) {
            System.err.println("Database connection failed in searchByNom. Détails: " + Shared.getLastErrorMessage());
            return null;
        }

        String sql = "SELECT * FROM `alimentation` WHERE nom = ? OR nom LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, nom);
            stmt.setString(2, nom + "%");
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                Alimentation food = new Alimentation(
                        rs.getInt("Id_ALIMENTATION"),
                        rs.getString("nom"),
                        rs.getDouble("calories"),
                        rs.getDouble("glucides"),
                        rs.getDouble("sucres"),
                        rs.getDouble("proteines"),
                        rs.getDouble("lipides")
                );
                System.out.println("Found food: " + food.getNom());
                return food;
            }
            System.out.println("No food found for: " + nom);
        } catch (SQLException e) {
            System.err.println("Error searching food: " + e.getMessage());
        } finally {
            Shared.deconnecter();
        }
        return null;
    }

    // Get all foods
    public static List<Alimentation> getAll() {
        Shared.connecter();
        List<Alimentation> list = new ArrayList<>();
        if (con == null) {
            System.err.println("Database connection failed in getAll. Détails: " + Shared.getLastErrorMessage());
            return list;
        }

        String sql = "SELECT * FROM `alimentation`";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Alimentation food = new Alimentation(
                        rs.getInt("Id_ALIMENTATION"),
                        rs.getString("nom"),
                        rs.getDouble("calories"),
                        rs.getDouble("glucides"),
                        rs.getDouble("sucres"),
                        rs.getDouble("proteines"),
                        rs.getDouble("lipides")
                );
                list.add(food);
            }
            System.out.println("Fetched " + list.size() + " foods");
        } catch (SQLException e) {
            System.err.println("Error fetching all foods: " + e.getMessage());
        } finally {
            Shared.deconnecter();
        }
        return list;
    }

    // Delete a food by name
    public static String deleteByNom(String nom) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            System.err.println(errorMsg);
            return errorMsg;
        }

        String sql = "DELETE FROM `alimentation` WHERE nom = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, nom);
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "Aliment supprimé avec succès" : "Aliment non trouvé";
        } catch (SQLException e) {
            System.err.println("Error deleting food: " + e.getMessage());
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    // Search foods by partial name match
    public static List<Alimentation> searchByPartialName(String partialName) {
        Shared.connecter();
        List<Alimentation> list = new ArrayList<>();
        if (con == null) {
            System.err.println("Database connection failed in searchByPartialName. Détails: " + Shared.getLastErrorMessage());
            return list;
        }

        String sql = "SELECT * FROM `alimentation` WHERE nom LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, "%" + partialName + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Alimentation food = new Alimentation(
                        rs.getInt("Id_ALIMENTATION"),
                        rs.getString("nom"),
                        rs.getDouble("calories"),
                        rs.getDouble("glucides"),
                        rs.getDouble("sucres"),
                        rs.getDouble("proteines"),
                        rs.getDouble("lipides")
                );
                list.add(food);
            }
            System.out.println("Found " + list.size() + " foods matching: " + partialName);
        } catch (SQLException e) {
            System.err.println("Error searching foods: " + e.getMessage());
        } finally {
            Shared.deconnecter();
        }
        return list;
    }
}