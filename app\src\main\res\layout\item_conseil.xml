<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Category Indicator -->
        <View
            android:id="@+id/categoryIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="#4CAF50"
            android:layout_marginEnd="12dp" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textTitre"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Titre du conseil"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/textContenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Contenu du conseil détaillé..."
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginBottom="8dp"
                android:lineSpacingExtra="2dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/textType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Type 1"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#999999"
                    android:background="#F5F5F5"
                    android:padding="4dp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/textCategorie"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Alimentation"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#4CAF50"
                    android:background="#E8F5E8"
                    android:padding="4dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/textDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2024-01-01"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:gravity="end" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
