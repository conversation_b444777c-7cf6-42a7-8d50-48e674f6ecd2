<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:elevation="4dp"
    android:backgroundTint="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- Icon -->
        <ImageView
            android:id="@+id/notification_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_notification_active"
            android:contentDescription="Notification Icon"
            android:background="@drawable/circle_background" />

        <!-- Text Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="12dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/textMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Message de notification"
                android:textColor="#000000"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/textType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Type: Rappel"
                android:textColor="#666666"
                android:textSize="14sp"
                android:layout_marginBottom="2dp" />

            <TextView
                android:id="@+id/textDateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2024-01-01 12:00:00"
                android:textColor="#777777"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>