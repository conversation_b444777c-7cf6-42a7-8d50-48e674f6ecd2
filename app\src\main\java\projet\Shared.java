package projet;

import android.os.StrictMode;
import android.util.Log;
import java.sql.Connection;
import java.sql.DriverManager;

public class Shared {
    public static Connection con;
    public static String lastErrorMessage;
    public static UTILISATEUR utilisateur; // Added utilisateur variable

    public static void connecter() {
        // Allow network operations on main thread (for testing only)
        StrictMode.ThreadPolicy policy = new StrictMode.ThreadPolicy.Builder().permitAll().build();
        StrictMode.setThreadPolicy(policy);

        if (con != null) {
            try {
                if (!con.isClosed()) {
                    Log.d("Shared", "Connection already established");
                    return;
                }
            } catch (Exception e) {
                Log.e("Shared", "Error checking connection state: " + e.getMessage(), e);
            }
        }

        lastErrorMessage = null;
        try {
            Log.d("Shared", "Loading JDBC driver...");
            Class.forName("com.mysql.jdbc.Driver");
            Log.d("Shared", "JDBC Driver loaded successfully");

            Log.d("Shared", "Connecting to database...");
            con = DriverManager.getConnection(
                    "********************************************************************************************",
                    "hh",
                    "12345678"
            );
            Log.d("Shared", "Database connected successfully");
        } catch (ClassNotFoundException e) {
            lastErrorMessage = "JDBC Driver not found: " + e.getMessage();
            Log.e("Shared", lastErrorMessage, e);
            con = null;
        } catch (Exception e) {
            lastErrorMessage = "Connection error: " + e.getMessage();
            Log.e("Shared", lastErrorMessage, e);
            con = null;
        }
    }

    public static void deconnecter() {
        try {
            if (con != null && !con.isClosed()) {
                con.close();
                Log.d("Shared", "Database disconnected successfully");
            }
        } catch (Exception e) {
            Log.e("Shared", "Disconnection error: " + e.getMessage(), e);
        } finally {
            con = null;
        }
    }

    public static String getLastErrorMessage() {
        return lastErrorMessage != null ? lastErrorMessage : "Unknown error";
    }

    // Add methods to manage utilisateur
    public static void setUtilisateur(UTILISATEUR user) {
        utilisateur = user;
        Log.d("Shared", "Utilisateur set: " + (user != null ? user.getId_UTILISATEUR() : "null"));
    }

    public static void clearUtilisateur() {
        utilisateur = null;
        Log.d("Shared", "Utilisateur cleared");
    }
}