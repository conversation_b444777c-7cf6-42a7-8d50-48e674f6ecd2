<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    android:background="@drawable/gradient_background"> <!-- خلفية متدرجة -->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:layout_margin="12dp"
        android:layoutDirection="rtl"
        android:elevation="8dp"
        android:clipToPadding="false">

        <!-- خانة اسم الوجبة -->
        <EditText
            android:id="@+id/etMealName"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="18dp"
            android:hint="اسم الوجبة"
            android:padding="16dp"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textDirection="rtl"
            android:inputType="text"
            android:imeOptions="actionSearch"
            android:background="@drawable/rounded_edittext_background" />

        <!-- عرض معلومات الوجبة -->
        <TextView
            android:id="@+id/tvMealInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="18dp"
            android:text="معلومات الوجبة ستظهر هنا"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textDirection="rtl"
            android:padding="16dp"
            android:background="@drawable/rounded_edittext_background" />

        <!-- زر الرجوع -->
        <Button
            android:id="@+id/btnBack1"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="⬅️ رجوع"
            android:textSize="17sp"
            android:textColor="@color/white"
            android:background="@drawable/rounded_button_red"
            android:layout_marginBottom="16dp" />

    </LinearLayout>
</ScrollView>
