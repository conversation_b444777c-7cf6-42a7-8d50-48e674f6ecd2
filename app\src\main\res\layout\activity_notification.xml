<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_main"
    android:padding="20dp">

    <!-- العنوان -->
    <TextView
        android:id="@+id/notification_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:text="Notifications"
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:textStyle="bold" />

    <!-- قائمة الإشعارات -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/notification_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/notification_title"
        android:layout_marginTop="20dp"
        android:background="#FFFFFF"
        android:padding="10dp"
        tools:ignore="SpeakableTextPresentCheck" />

</RelativeLayout>