<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_main"
    android:orientation="vertical"
    android:padding="20dp">

    <!-- العنوان -->
    <TextView
        android:id="@+id/notification_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:text="Notifications"
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:textStyle="bold" />

    <!-- حقول إضافة إشعار جديد -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#FFFFFF"
        android:padding="15dp"
        android:layout_marginBottom="20dp"
        android:elevation="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ajouter une notification"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="10dp" />

        <EditText
            android:id="@+id/editMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Message de notification"
            android:background="@drawable/custom_edittext"
            android:padding="12dp"
            android:layout_marginBottom="10dp"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/editType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Type (ex: Rappel, Alerte, Info)"
            android:background="@drawable/custom_edittext"
            android:padding="12dp"
            android:layout_marginBottom="15dp"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnAddNotification"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Ajouter"
                android:backgroundTint="#4CAF50"
                android:textColor="#FFFFFF"
                android:layout_marginEnd="10dp" />

            <Button
                android:id="@+id/btnBack"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Retour"
                android:backgroundTint="#FF6200EE"
                android:textColor="#FFFFFF" />

        </LinearLayout>

    </LinearLayout>

    <!-- قائمة الإشعارات -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewNotifications"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#FFFFFF"
        android:padding="10dp"
        android:elevation="4dp"
        tools:ignore="SpeakableTextPresentCheck" />

</LinearLayout>