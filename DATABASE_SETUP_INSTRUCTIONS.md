# 🔧 تعليمات إعداد قاعدة البيانات - تطبيق إدارة السكري

## 📋 الملفات المطلوبة

1. **database_fixes.sql** - سكريبت إصلاح قاعدة البيانات
2. **database_verification.sql** - سكريبت التحقق من صحة البيانات

## 🚀 خطوات التطبيق

### **الخطوة 1: تطبيق الإصلاحات**

1. افتح **phpMyAdmin**
2. اختر قاعدة البيانات `projet`
3. اذهب إلى تبويب **SQL**
4. انسخ والصق محتوى ملف `database_fixes.sql`
5. اضغط **تنفيذ (Execute)**

### **الخطوة 2: التحقق من النتائج**

1. في نفس تبويب **SQL**
2. انسخ والصق محتوى ملف `database_verification.sql`
3. اضغط **تنفيذ (Execute)**
4. راجع النتائج للتأكد من نجاح العملية

## ✅ النتائج المتوقعة

### **الجداول الجديدة/المُصلحة:**

| الجدول | الحالة | الوصف |
|--------|--------|--------|
| `alerte` | ✅ مُصلح | إنذارات الجلايسيميا |
| `notification` | ✅ مُصلح | التنبيهات |
| `alimentation` | ✅ مُصلح | بيانات الأطعمة |
| `conseil` | ✅ جديد | النصائح الطبية |

### **البيانات المُضافة:**

- **19 نصيحة طبية** موزعة على:
  - نصائح للنوع الأول (5 نصائح)
  - نصائح للنوع الثاني (5 نصائح)
  - نصائح عامة (5 نصائح)
  - نصائح التغذية (2 نصيحة)
  - نصائح الرياضة (2 نصيحة)

- **بيانات الأطعمة** محولة إلى الهيكل الجديد

## 🔍 التحقق من النجاح

بعد تطبيق السكريبت، يجب أن ترى:

```sql
-- عدد السجلات في كل جدول
utilisateur: [عدد المستخدمين الحاليين]
glycemie: [عدد قياسات السكر الحالية]
historique: [عدد السجلات التاريخية]
alerte: 0 (جدول فارغ جديد)
notification: 0 (جدول فارغ جديد)
alimentation: [عدد الأطعمة المحولة]
conseil: 19 (النصائح الجديدة)
```

## ⚠️ ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من عمل نسخة احتياطية قبل التطبيق
2. **البيانات القديمة**: سيتم حذف البيانات القديمة في الجداول المُصلحة
3. **المفاتيح الخارجية**: تم إضافة قيود المفاتيح الخارجية للحفاظ على تكامل البيانات

## 🐛 حل المشاكل

### **خطأ في المفاتيح الخارجية:**
```sql
-- إذا ظهر خطأ في المفاتيح الخارجية، قم بتشغيل:
SET FOREIGN_KEY_CHECKS = 0;
-- ثم أعد تشغيل السكريبت
SET FOREIGN_KEY_CHECKS = 1;
```

### **خطأ في تحويل البيانات:**
```sql
-- إذا فشل تحويل بيانات الأطعمة، قم بحذف الجدول وإعادة إنشائه:
DROP TABLE IF EXISTS `alimentation`;
-- ثم أعد تشغيل جزء إنشاء جدول alimentation من السكريبت
```

## 📱 اختبار التطبيق

بعد تطبيق الإصلاحات:

1. **شغل التطبيق**
2. **اختبر الوظائف التالية:**
   - تسجيل الدخول
   - إضافة قياس جلايسيميا
   - عرض الإنذارات (إذا كانت القيمة خارج النطاق الطبيعي)
   - عرض التنبيهات
   - البحث عن الأطعمة
   - عرض النصائح الطبية

## 🎯 النتيجة النهائية

✅ **قاعدة بيانات محدثة ومتكاملة**  
✅ **جميع الكلاسات مربوطة بشكل صحيح**  
✅ **أمان محسن مع PreparedStatement**  
✅ **بيانات أولية للنصائح الطبية**  
✅ **فهارس لتحسين الأداء**

---

**ملاحظة:** إذا واجهت أي مشاكل، تأكد من أن إصدار MySQL/MariaDB يدعم جميع الميزات المستخدمة في السكريبت.
