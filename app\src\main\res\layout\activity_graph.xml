<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Graphique des niveaux de glycémie"
        android:textSize="20sp"
        android:textColor="#000000"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/glycemie_chart"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>