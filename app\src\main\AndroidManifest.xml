<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.myapp">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyApp">

        <activity
            android:name=".DiabetesTipsActivity"
            android:exported="false" /> <!-- MainActivity كنشاط افتراضي -->
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".LoginActivity" />
        <activity android:name=".RegisterActivity" />
        <activity
            android:name=".GlycemieActivity"
            android:exported="false" />
        <activity
            android:name=".HistoriqueActivity"
            android:exported="false" />
        <activity
            android:name=".UtilisateurActivity"
            tools:ignore="MissingClass" />
        <activity
            android:name=".AlimentationActivity"
            android:exported="false"
            tools:ignore="Instantiatable" />
        <activity
            android:name=".NotificationActivity"
            android:exported="false" />
        <activity
            android:name=".AlerteActivity"
            android:exported="false" />
        <activity android:name=".GraphActivity" /> <!-- تسجيل BroadcastReceiver -->
        <receiver
            android:name=".NotificationReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
    </application>

</manifest>