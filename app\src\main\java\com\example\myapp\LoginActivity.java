package com.example.myapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import projet.UTILISATEUR;
import projet.Shared;

public class LoginActivity extends AppCompatActivity {
    private EditText emailInput, passwordInput;
    private Button loginButton, registerButton;
    private Handler mainHandler;
    private static final String TAG = "LoginActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        emailInput = findViewById(R.id.email_input);
        passwordInput = findViewById(R.id.password_input);
        loginButton = findViewById(R.id.login_button);
        registerButton = findViewById(R.id.register_button);
        mainHandler = new Handler(Looper.getMainLooper());

        loginButton.setOnClickListener(v -> {
            String email = emailInput.getText().toString().trim().toLowerCase();
            String password = passwordInput.getText().toString().trim().toLowerCase();

            if (email.length() == 0 || password.length() == 0) {
                Toast.makeText(LoginActivity.this, "Veuillez remplir tous les champs", Toast.LENGTH_SHORT).show();
                return;
            }

            Log.d(TAG, "Attempting login with email: " + email + ", password: " + password);

            new Thread(() -> {
                UTILISATEUR utilisateur = UTILISATEUR.authentifier(email, password);
                mainHandler.post(() -> {
                    Log.d(TAG, "Authentication result: " + (utilisateur != null ? "Success" : "Failure"));
                    if (utilisateur != null) {
                        // Save user data to SharedPreferences
                        SharedPreferences prefs = getSharedPreferences("UserPrefs", MODE_PRIVATE);
                        SharedPreferences.Editor editor = prefs.edit();
                        editor.putString("email", utilisateur.getEmail());
                        editor.putString("nom", utilisateur.getNom());
                        editor.putString("type_diabete", utilisateur.getType_diabète());
                        editor.apply();

                        Shared.utilisateur = utilisateur;
                        Toast.makeText(LoginActivity.this, "Connexion réussie", Toast.LENGTH_SHORT).show();
                        Intent intent = new Intent(LoginActivity.this, MainActivity.class);
                        startActivity(intent);
                        finish();
                    } else {
                        String errorMessage = Shared.getLastErrorMessage();
                        Log.d(TAG, "Error message: " + (errorMessage != null ? errorMessage : "No error message"));
                        if (errorMessage != null && errorMessage.contains("Connection error")) {
                            Toast.makeText(LoginActivity.this, "Erreur de connexion à la base de données : " + errorMessage, Toast.LENGTH_LONG).show();
                        } else {
                            Toast.makeText(LoginActivity.this, "Email ou mot de passe incorrect", Toast.LENGTH_LONG).show();
                        }
                    }
                });
            }).start();
        });

        registerButton.setOnClickListener(v -> {
            Intent intent = new Intent(LoginActivity.this, RegisterActivity.class);
            startActivity(intent);
        });
    }
}