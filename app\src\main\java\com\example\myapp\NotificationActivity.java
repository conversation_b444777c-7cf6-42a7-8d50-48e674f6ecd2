package com.example.myapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;
import projet.NOTIFICATION;
import projet.Shared;

public class NotificationActivity extends AppCompatActivity {

    private static final String TAG = "NotificationActivity";
    private RecyclerView recyclerViewNotifications;
    private NotificationAdapter notificationAdapter;
    private ArrayList<NOTIFICATION> notificationList;
    private EditText editMessage, editType;
    private Button btnAddNotification, btnBack;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notification);

        // Initialize views
        recyclerViewNotifications = findViewById(R.id.recyclerViewNotifications);
        editMessage = findViewById(R.id.editMessage);
        editType = findViewById(R.id.editType);
        btnAddNotification = findViewById(R.id.btnAddNotification);
        btnBack = findViewById(R.id.btnBack);
        mainHandler = new Handler(Looper.getMainLooper());

        // Initialize notification list
        notificationList = new ArrayList<>();

        // Setup RecyclerView
        setupRecyclerView();

        // Load notifications
        loadNotifications();

        // Setup button listeners
        setupButtonListeners();
    }

    private void setupRecyclerView() {
        notificationAdapter = new NotificationAdapter(notificationList);
        recyclerViewNotifications.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewNotifications.setAdapter(notificationAdapter);
    }

    private void loadNotifications() {
        // Get current user ID from SharedPreferences
        SharedPreferences prefs = getSharedPreferences("UserPrefs", MODE_PRIVATE);
        String email = prefs.getString("email", "");

        if (email.isEmpty()) {
            Toast.makeText(this, "Utilisateur non connecté", Toast.LENGTH_SHORT).show();
            return;
        }

        new Thread(() -> {
            try {
                // Get user ID from Shared.utilisateur or authenticate again
                int userId = Shared.utilisateur != null ? Shared.utilisateur.getId_UTILISATEUR() : -1;

                if (userId == -1) {
                    // Try to get user data
                    String password = prefs.getString("password", "");
                    if (!password.isEmpty()) {
                        projet.UTILISATEUR user = projet.UTILISATEUR.authentifier(email, password);
                        if (user != null) {
                            userId = user.getId_UTILISATEUR();
                            Shared.utilisateur = user;
                        }
                    }
                }

                if (userId != -1) {
                    ArrayList<NOTIFICATION> notifications = NOTIFICATION.getAllForUser(userId);
                    mainHandler.post(() -> {
                        notificationList.clear();
                        notificationList.addAll(notifications);
                        notificationAdapter.notifyDataSetChanged();
                        Log.d(TAG, "Loaded " + notifications.size() + " notifications");
                    });
                } else {
                    mainHandler.post(() -> {
                        Toast.makeText(this, "Impossible de charger les notifications", Toast.LENGTH_SHORT).show();
                    });
                }
            } catch (Exception e) {
                Log.e(TAG, "Error loading notifications: " + e.getMessage(), e);
                mainHandler.post(() -> {
                    Toast.makeText(this, "Erreur lors du chargement: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }

    private void setupButtonListeners() {
        btnAddNotification.setOnClickListener(v -> addNotification());

        btnBack.setOnClickListener(v -> {
            Intent intent = new Intent(NotificationActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        });
    }

    private void addNotification() {
        String message = editMessage.getText().toString().trim();
        String type = editType.getText().toString().trim();

        if (message.isEmpty() || type.isEmpty()) {
            Toast.makeText(this, "Veuillez remplir tous les champs", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get current user ID
        int userId = Shared.utilisateur != null ? Shared.utilisateur.getId_UTILISATEUR() : -1;
        if (userId == -1) {
            Toast.makeText(this, "Utilisateur non connecté", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get current date and time
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        String currentDateTime = sdf.format(new Date());

        // Create notification object
        NOTIFICATION notification = new NOTIFICATION(message, type, currentDateTime, userId);

        new Thread(() -> {
            try {
                String result = NOTIFICATION.add(notification);
                mainHandler.post(() -> {
                    Toast.makeText(this, result, Toast.LENGTH_SHORT).show();
                    if (result.contains("succès")) {
                        // Clear input fields
                        editMessage.setText("");
                        editType.setText("");

                        // Reload notifications
                        loadNotifications();
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Error adding notification: " + e.getMessage(), e);
                mainHandler.post(() -> {
                    Toast.makeText(this, "Erreur lors de l'ajout: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }
}