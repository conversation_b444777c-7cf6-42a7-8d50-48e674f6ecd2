package com.example.myapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.gson.Gson;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import projet.GLYCEMIE;
import projet.Historique;
import projet.Shared;

public class HistoriqueActivity extends AppCompatActivity {

    private static final String TAG = "HistoriqueActivity";
    private static final String ACTION_NEW_GLYCEMIE = "com.example.myapp.NEW_GLYCEMIE";
    private RecyclerView recyclerView;
    private HistoriqueAdapter adapter;
    private ArrayList<Historique> historiqueList;
    private Handler mainHandler;
    private EditText searchInput;
    private Button searchButton;
    private Button deleteButton;
    private Button graphButton;
    private BroadcastReceiver glycemieReceiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.historiqueactivity);

        recyclerView = findViewById(R.id.glycemie_recycler_view);
        searchInput = findViewById(R.id.search_input);
        searchButton = findViewById(R.id.search_button);
        deleteButton = findViewById(R.id.delete_button);
        graphButton = findViewById(R.id.graph_button);

        historiqueList = new ArrayList<>();
        adapter = new HistoriqueAdapter(convertToAdapterFormat(historiqueList));
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
        mainHandler = new Handler(Looper.getMainLooper());

        // Set click listeners
        searchButton.setOnClickListener(v -> performSearch());
        deleteButton.setOnClickListener(v -> deleteSelectedItems());
        graphButton.setOnClickListener(v -> showGraph());

        loadHistoriqueData();

        // Register BroadcastReceiver with RECEIVER_NOT_EXPORTED flag
        glycemieReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ACTION_NEW_GLYCEMIE.equals(intent.getAction())) {
                    String json = intent.getStringExtra("new_glycemie");
                    GLYCEMIE newGlycemi = new Gson().fromJson(json, GLYCEMIE.class);
                    if (newGlycemi != null) {
                        Historique newHistorique = new Historique(
                                0, // Id_HISTORIQUE will be auto-generated in DB
                                newGlycemi.getValeur(),
                                newGlycemi.getDate(),
                                newGlycemi.getHeure(),
                                newGlycemi.getBefore_after(),
                                newGlycemi.getId_UTILISATEUR()
                        );
                        historiqueList.add(0, newHistorique); // Add at the beginning for latest first
                        adapter.updateList(convertToAdapterFormat(historiqueList));
                        Log.d(TAG, "Received new glycemie, updated list: " + newHistorique.toString());
                        Toast.makeText(HistoriqueActivity.this, "Nouvelle mesure ajoutée", Toast.LENGTH_SHORT).show();
                    } else {
                        Log.e(TAG, "Failed to parse new glycemie from broadcast");
                    }
                }
            }
        };
        IntentFilter filter = new IntentFilter(ACTION_NEW_GLYCEMIE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            registerReceiver(glycemieReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (glycemieReceiver != null) {
            unregisterReceiver(glycemieReceiver);
        }
    }

    private void loadHistoriqueData() {
        if (Shared.utilisateur == null) {
            Toast.makeText(this, "Utilisateur non connecté", Toast.LENGTH_LONG).show();
            Intent intent = new Intent(HistoriqueActivity.this, LoginActivity.class);
            startActivity(intent);
            finish();
            return;
        }

        int userId = Shared.utilisateur.getId_UTILISATEUR();
        Log.d(TAG, "Loading historical data for userId: " + userId);
        new Thread(() -> {
            ArrayList<Historique> result = Historique.Chercher_Id_UTILISATEUR(String.valueOf(userId)); // Temporary fallback
            mainHandler.post(() -> {
                if (result.isEmpty()) {
                    Log.w(TAG, "No historical data found for userId: " + userId);
                    Toast.makeText(HistoriqueActivity.this, "Aucune mesure de glycémie trouvée", Toast.LENGTH_LONG).show();
                } else {
                    Log.d(TAG, "Loaded " + result.size() + " historical records");
                    historiqueList.clear();
                    historiqueList.addAll(result);
                    adapter.updateList(convertToAdapterFormat(historiqueList));
                }
            });
        }).start();
    }

    private void performSearch() {
        String searchDate = searchInput.getText().toString().trim();
        if (searchDate.isEmpty()) {
            Toast.makeText(this, "Veuillez entrer une date", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Searching for date: " + searchDate);
        new Thread(() -> {
            ArrayList<Historique> result = Historique.Chercher_Date(searchDate);
            mainHandler.post(() -> {
                if (result.isEmpty()) {
                    Log.w(TAG, "No data found for date: " + searchDate);
                    Toast.makeText(HistoriqueActivity.this, "Aucune mesure trouvée pour cette date", Toast.LENGTH_LONG).show();
                    adapter.updateList(convertToAdapterFormat(historiqueList)); // Reset to full list
                } else {
                    Log.d(TAG, "Found " + result.size() + " records for date: " + searchDate);
                    historiqueList.clear();
                    historiqueList.addAll(result);
                    adapter.updateList(convertToAdapterFormat(historiqueList));
                }
            });
        }).start();
    }

    private void deleteSelectedItems() {
        List<HistoriqueAdapter.GlycemieEntry> selectedEntries = adapter.getSelectedItems();
        if (selectedEntries.isEmpty()) {
            Toast.makeText(this, "Aucune mesure sélectionnée", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Deleting " + selectedEntries.size() + " selected items");
        new Thread(() -> {
            for (HistoriqueAdapter.GlycemieEntry entry : selectedEntries) {
                String dateTime = entry.dateTime.trim();
                String[] parts = dateTime.split(" ");
                if (parts.length >= 2) {
                    String date = parts[0];
                    String heure = parts[1];
                    for (Historique h : new ArrayList<>(historiqueList)) {
                        if (h.getDate().equals(date) && h.getHeure().equals(heure)) {
                            Historique.Delete(h); // Assuming Historique has a Delete method
                            historiqueList.remove(h);
                            Log.d(TAG, "Deleted entry - Date: " + date + ", Heure: " + heure);
                            break;
                        }
                    }
                }
            }
            mainHandler.post(() -> {
                adapter.updateList(convertToAdapterFormat(historiqueList));
                Toast.makeText(HistoriqueActivity.this, "Mesures supprimées avec succès", Toast.LENGTH_SHORT).show();
            });
        }).start();
    }

    private void showGraph() {
        Log.d(TAG, "Preparing data for GraphActivity");
        ArrayList<HistoriqueAdapter.GlycemieEntry> graphData = convertToAdapterFormat(historiqueList);
        Gson gson = new Gson();
        String json = gson.toJson(graphData);
        Intent intent = new Intent(this, GraphActivity.class);
        intent.putExtra("glycemie_list", json);
        startActivity(intent);
        Log.d(TAG, "Started GraphActivity with " + graphData.size() + " entries");
    }

    private ArrayList<HistoriqueAdapter.GlycemieEntry> convertToAdapterFormat(ArrayList<Historique> list) {
        ArrayList<HistoriqueAdapter.GlycemieEntry> adaptedList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.getDefault());
        for (Historique h : list) {
            String dateTime = h.getDate() + " " + h.getHeure();
            long timestamp = 0;
            try {
                Date date = sdf.parse(dateTime + ":00"); // Append seconds as 00 to match format
                if (date != null) {
                    timestamp = date.getTime();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error parsing dateTime: " + dateTime, e);
                timestamp = System.currentTimeMillis(); // Fallback
            }
            adaptedList.add(new HistoriqueAdapter.GlycemieEntry(
            ));
            Log.d(TAG, "Converted - DateTime: " + dateTime + ", Value: " + h.getValeur() + ", Timestamp: " + timestamp);
        }
        return adaptedList;
    }
}