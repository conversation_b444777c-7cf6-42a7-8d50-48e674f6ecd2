package projet;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import android.util.Log;
import static projet.Shared.con;

/**
 * <AUTHOR>
 */
public class ALERTE {
    int Id_ALERTE;
    double valeur_glycemie;
    String type_alerte; // "HYPOGLYCEMIE", "HYPERGLYCEMIE", "NORMAL"
    String message;
    String date_heure;
    int Id_UTILISATEUR;
    
    static ArrayList<ALERTE> alertes = new ArrayList<>();

    // Constructor for creating new alert (without ID)
    public ALERTE(double valeur_glycemie, String type_alerte, String message, String date_heure, int Id_UTILISATEUR) {
        this.valeur_glycemie = valeur_glycemie;
        this.type_alerte = type_alerte;
        this.message = message;
        this.date_heure = date_heure;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    // Constructor for retrieving from database (with ID)
    public ALERTE(int Id_ALERTE, double valeur_glycemie, String type_alerte, String message, String date_heure, int Id_UTILISATEUR) {
        this.Id_ALERTE = Id_ALERTE;
        this.valeur_glycemie = valeur_glycemie;
        this.type_alerte = type_alerte;
        this.message = message;
        this.date_heure = date_heure;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    // Getters and setters
    public int getId_ALERTE() {
        return Id_ALERTE;
    }

    public void setId_ALERTE(int Id_ALERTE) {
        this.Id_ALERTE = Id_ALERTE;
    }

    public double getValeur_glycemie() {
        return valeur_glycemie;
    }

    public void setValeur_glycemie(double valeur_glycemie) {
        this.valeur_glycemie = valeur_glycemie;
    }

    public String getType_alerte() {
        return type_alerte;
    }

    public void setType_alerte(String type_alerte) {
        this.type_alerte = type_alerte;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDate_heure() {
        return date_heure;
    }

    public void setDate_heure(String date_heure) {
        this.date_heure = date_heure;
    }

    public int getId_UTILISATEUR() {
        return Id_UTILISATEUR;
    }

    public void setId_UTILISATEUR(int Id_UTILISATEUR) {
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    public static ArrayList<ALERTE> getAlertes() {
        return alertes;
    }

    public static void setAlertes(ArrayList<ALERTE> alertes) {
        ALERTE.alertes = alertes;
    }

    @Override
    public String toString() {
        return "ALERTE{" +
                "Id_ALERTE=" + Id_ALERTE +
                ", valeur_glycemie=" + valeur_glycemie +
                ", type_alerte='" + type_alerte + '\'' +
                ", message='" + message + '\'' +
                ", date_heure='" + date_heure + '\'' +
                ", Id_UTILISATEUR=" + Id_UTILISATEUR +
                '}';
    }

    // Database operations
    public static String add(ALERTE alerte) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("ALERTE", errorMsg);
            return errorMsg;
        }

        String sql = "INSERT INTO `alerte` (`valeur_glycemie`, `type_alerte`, `message`, `date_heure`, `Id_UTILISATEUR`) VALUES (?, ?, ?, ?, ?)";
        try {
            PreparedStatement stmt = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setDouble(1, alerte.getValeur_glycemie());
            stmt.setString(2, alerte.getType_alerte());
            stmt.setString(3, alerte.getMessage());
            stmt.setString(4, alerte.getDate_heure());
            stmt.setInt(5, alerte.getId_UTILISATEUR());

            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    alerte.setId_ALERTE(rs.getInt(1));
                }
                return "Alerte ajoutée avec succès";
            } else {
                return "Échec de l'ajout de l'alerte";
            }
        } catch (Exception e) {
            Log.e("ALERTE", "Error adding alerte: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static ArrayList<ALERTE> getAllForUser(int userId) {
        ArrayList<ALERTE> alerteList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("ALERTE", "Database connection failed in getAllForUser. Détails: " + Shared.getLastErrorMessage());
            return alerteList;
        }

        String sql = "SELECT * FROM `alerte` WHERE Id_UTILISATEUR = ? ORDER BY date_heure DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, userId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                ALERTE alerte = new ALERTE(
                        rs.getInt("Id_ALERTE"),
                        rs.getDouble("valeur_glycemie"),
                        rs.getString("type_alerte"),
                        rs.getString("message"),
                        rs.getString("date_heure"),
                        rs.getInt("Id_UTILISATEUR")
                );
                alerteList.add(alerte);
            }
        } catch (Exception e) {
            Log.e("ALERTE", "Error fetching alertes for user: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return alerteList;
    }

    // Method to determine alert type based on glycemia value
    public static String determineAlertType(double glycemiaValue) {
        if (glycemiaValue < 70) {
            return "HYPOGLYCEMIE";
        } else if (glycemiaValue > 180) {
            return "HYPERGLYCEMIE";
        } else {
            return "NORMAL";
        }
    }

    // Method to generate alert message based on type
    public static String generateAlertMessage(String alertType, double value) {
        switch (alertType) {
            case "HYPOGLYCEMIE":
                return "ATTENTION: Hypoglycémie détectée (" + value + " mg/dL). Consommez du sucre rapide immédiatement!";
            case "HYPERGLYCEMIE":
                return "ATTENTION: Hyperglycémie détectée (" + value + " mg/dL). Consultez votre médecin!";
            default:
                return "Glycémie normale (" + value + " mg/dL).";
        }
    }

    // Method to create alert automatically from glycemia reading
    public static String createAlertFromGlycemia(double glycemiaValue, int userId, String dateTime) {
        String alertType = determineAlertType(glycemiaValue);
        String message = generateAlertMessage(alertType, glycemiaValue);
        
        // Only create alert for abnormal values
        if (!alertType.equals("NORMAL")) {
            ALERTE alerte = new ALERTE(glycemiaValue, alertType, message, dateTime, userId);
            return add(alerte);
        }
        
        return "Glycémie normale, aucune alerte créée";
    }
}
