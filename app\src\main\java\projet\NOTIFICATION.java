package projet;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import android.util.Log;
import static projet.Shared.con;

/**
 * <AUTHOR>
 */
public class NOTIFICATION {
    int Id_NOTIFICATION; // Changed to int for AUTO_INCREMENT
    String message;
    String type;
    String date_heure;
    int Id_UTILISATEUR; // Added to link notifications to users
    static ArrayList<NOTIFICATION> notifecation = new ArrayList<>();
    static ArrayList<NOTIFICATION> L = new ArrayList<>();

    // Constructor for creating new notification (without ID)
    public NOTIFICATION(String message, String type, String date_heure, int Id_UTILISATEUR) {
        this.message = message;
        this.type = type;
        this.date_heure = date_heure;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    // Constructor for retrieving from database (with ID)
    public NOTIFICATION(int Id_NOTIFICATION, String message, String type, String date_heure, int Id_UTILISATEUR) {
        this.Id_NOTIFICATION = Id_NOTIFICATION;
        this.message = message;
        this.type = type;
        this.date_heure = date_heure;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    @Override
    public String toString() {
        return "NOTIFICATION{" +
                "Id_NOTIFICATION=" + Id_NOTIFICATION +
                ", message='" + message + '\'' +
                ", type='" + type + '\'' +
                ", date_heure='" + date_heure + '\'' +
                ", Id_UTILISATEUR=" + Id_UTILISATEUR +
                '}';
    }

    // Getters and setters
    public int getId_NOTIFICATION() {
        return Id_NOTIFICATION;
    }

    public void setId_NOTIFICATION(int Id_NOTIFICATION) {
        this.Id_NOTIFICATION = Id_NOTIFICATION;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDate_heure() {
        return date_heure;
    }

    public void setDate_heure(String date_heure) {
        this.date_heure = date_heure;
    }

    public int getId_UTILISATEUR() {
        return Id_UTILISATEUR;
    }

    public void setId_UTILISATEUR(int Id_UTILISATEUR) {
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    public static ArrayList<NOTIFICATION> getNotifecation() {
        return notifecation;
    }

    public static void setNotifecation(ArrayList<NOTIFICATION> notifecation) {
        NOTIFICATION.notifecation = notifecation;
    }

    public static ArrayList<NOTIFICATION> getL() {
        return L;
    }

    public static void setL(ArrayList<NOTIFICATION> L) {
        NOTIFICATION.L = L;
    }
    public static String add(NOTIFICATION notification) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("NOTIFICATION", errorMsg);
            return errorMsg;
        }

        String sql = "INSERT INTO `notification` (`message`, `type`, `date_heure`, `Id_UTILISATEUR`) VALUES (?, ?, ?, ?)";
        try {
            PreparedStatement stmt = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, notification.getMessage());
            stmt.setString(2, notification.getType());
            stmt.setString(3, notification.getDate_heure());
            stmt.setInt(4, notification.getId_UTILISATEUR());

            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    notification.setId_NOTIFICATION(rs.getInt(1));
                }
                return "Notification ajoutée avec succès";
            } else {
                return "Échec de l'ajout de la notification";
            }
        } catch (Exception e) {
            Log.e("NOTIFICATION", "Error adding notification: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static String Delete(NOTIFICATION notification) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("NOTIFICATION", errorMsg);
            return errorMsg;
        }

        String sql = "DELETE FROM `notification` WHERE Id_NOTIFICATION = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, notification.getId_NOTIFICATION());
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "Notification supprimée avec succès" : "Notification non trouvée";
        } catch (Exception e) {
            Log.e("NOTIFICATION", "Error deleting notification: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }


    public static ArrayList<NOTIFICATION> getAllForUser(int userId) {
        ArrayList<NOTIFICATION> notificationList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("NOTIFICATION", "Database connection failed in getAllForUser. Détails: " + Shared.getLastErrorMessage());
            return notificationList;
        }

        String sql = "SELECT * FROM `notification` WHERE Id_UTILISATEUR = ? ORDER BY date_heure DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, userId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                NOTIFICATION notification = new NOTIFICATION(
                        rs.getInt("Id_NOTIFICATION"),
                        rs.getString("message"),
                        rs.getString("type"),
                        rs.getString("date_heure"),
                        rs.getInt("Id_UTILISATEUR")
                );
                notificationList.add(notification);
            }
        } catch (Exception e) {
            Log.e("NOTIFICATION", "Error fetching notifications for user: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return notificationList;
    }

    public static ArrayList<NOTIFICATION> getAll() {
        ArrayList<NOTIFICATION> notificationList = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("NOTIFICATION", "Database connection failed in getAll. Détails: " + Shared.getLastErrorMessage());
            return notificationList;
        }

        String sql = "SELECT * FROM `notification` ORDER BY date_heure DESC";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                NOTIFICATION notification = new NOTIFICATION(
                        rs.getInt("Id_NOTIFICATION"),
                        rs.getString("message"),
                        rs.getString("type"),
                        rs.getString("date_heure"),
                        rs.getInt("Id_UTILISATEUR")
                );
                notificationList.add(notification);
            }
        } catch (Exception e) {
            Log.e("NOTIFICATION", "Error fetching all notifications: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return notificationList;
    }



    public static ArrayList<NOTIFICATION>  Chercher_Id_NOTIFICATION(String NOT)  {

        String req="SELECT * FROM `notifIcation` WHERE  Id_NOTIFICATION like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> l=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("date_heure");


                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                l.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return l;

    }

    public static ArrayList<NOTIFICATION>  Chercher_Message(String NOT)  {

        String req="SELECT * FROM `notification` WHERE  Message like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> L=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("date_heure");


                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                L.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return L;

    }

    public static ArrayList<NOTIFICATION>  Chercher_type(String NOT)  {

        String req="SELECT * FROM `notification` WHERE  type like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> l=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("Date_Heure");

                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                L.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return l;

    }

    public static ArrayList<NOTIFICATION>  Chercher_date_heure(String NOT)  {

        String req="SELECT * FROM `notification` WHERE  date_heure like'"+
                NOT+"%'";
        ArrayList<NOTIFICATION> l=new ArrayList<NOTIFICATION>();
        Shared.connecter();
        try{
            Statement stmt=con.createStatement();
            java.sql.ResultSet rs=stmt.executeQuery(req);
            while(rs.next()){
                String in=rs.getString("Id_NOTIFICATION");
                String me=rs.getString("Message");
                String ty=rs.getString("type");
                String dh=rs.getString("Date_Heure");

                NOTIFICATION notification=new NOTIFICATION(in,me,ty,dh);
                L.add(notification);}
        }
        catch(Exception e){System.out.println(e);}
        Shared.deconnecter();
        return l;

    }


    public static String Modifier_NOTIFICATION (NOTIFICATION NOT){
        String req=
                "UPDATE `notification` SET " +

                        "`Message`='" + NOT.getMessage()+ "', " +
                        "`type`='" + NOT.getType()+ "', " +
                        "`Date_Heure`='" + NOT.getDate_heure()+ "', " +
                        "WHERE `Id_NOTIFICATION`='" +NOT.getId_NOTIFICATION()+ "';";
        System.out.print(req);
        Shared.connecter();
        try{

            Statement stmt=con.createStatement();
            stmt.executeUpdate(req);

        } catch(Exception e){
            return e.toString();}
        Shared.deconnecter();
        return "NOTIFICATION sauvgarde avec succees";}




    public static void main(String[] args) {
        NOTIFICATION notification =new NOTIFICATION("fff","hhgggg","gg","khhhha");
        String msg=
                NOTIFICATION.add(notification);

 /* NOTIFICATION NOTIFICATION=new NOTIFICATION("hhh","hhh","hhhh","khhhha");
        NOTIFICATION.setId_NOTIFICATION("hhh");
        String result = Delete(NOTIFICATION);
        System.out.println(result);
  /* ArrayList<NOTIFICATION> NOTIFICATIONTROUVER = Chercher_Id_NOTIFICATION("55");

        if (NOTIFICATIONTROUVER.isEmpty()) {
            System.out.println("Aucun NOTIFICATION trouvé avec le code : " + "gggg");
        } else {
            System.out.println("alerte trouvés avec le code '" +" gggg" + "' :");
            for (NOTIFICATION NOT : NOTIFICATIONTROUVER) {
                System.out.println(NOT); */
    }
}






