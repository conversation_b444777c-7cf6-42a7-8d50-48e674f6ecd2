package projet;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import android.util.Log;

import static projet.Shared.con;

/**
 * <AUTHOR>
 */
public class Historique {
    int Id_HISTORIQUE; // Changed to int to match AUTO_INCREMENT PRIMARY KEY
    double valeur; // Changed to double to match DECIMAL(5,2)
    String date; // Split from Date_et_heure
    String heure; // Split from Date_et_heure
    String before_after; // Added to match table
    int Id_UTILISATEUR; // Added to match table

    static ArrayList<Historique> h = new ArrayList<>();
    static ArrayList<Historique> l = new ArrayList<>();

    // Constructor for creating a new record (without Id_HISTORIQUE, auto-generated)
    public Historique(double valeur, String date, String heure, String before_after, int Id_UTILISATEUR) {
        this.valeur = valeur;
        this.date = date;
        this.heure = heure;
        this.before_after = before_after;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    // Constructor for retrieving from database (includes Id_HISTORIQUE)
    public Historique(int Id_HISTORIQUE, double valeur, String date, String heure, String before_after, int Id_UTILISATEUR) {
        this.Id_HISTORIQUE = Id_HISTORIQUE;
        this.valeur = valeur;
        this.date = date;
        this.heure = heure;
        this.before_after = before_after;
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    // Getters and setters
    public int getId_HISTORIQUE() {
        return Id_HISTORIQUE;
    }

    public void setId_HISTORIQUE(int Id_HISTORIQUE) {
        this.Id_HISTORIQUE = Id_HISTORIQUE;
    }

    public double getValeur() {
        return valeur;
    }

    public void setValeur(double valeur) {
        this.valeur = valeur;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getHeure() {
        return heure;
    }

    public void setHeure(String heure) {
        this.heure = heure;
    }

    public String getBefore_after() {
        return before_after;
    }

    public void setBefore_after(String before_after) {
        this.before_after = before_after;
    }

    public int getId_UTILISATEUR() {
        return Id_UTILISATEUR;
    }

    public void setId_UTILISATEUR(int Id_UTILISATEUR) {
        this.Id_UTILISATEUR = Id_UTILISATEUR;
    }

    public static ArrayList<Historique> getH() {
        return h;
    }

    public static void setH(ArrayList<Historique> h) {
        Historique.h = h;
    }

    public static ArrayList<Historique> getL() {
        return l;
    }

    public static void setL(ArrayList<Historique> l) {
        Historique.l = l;
    }

    @Override
    public String toString() {
        return "Historique{" +
                "Id_HISTORIQUE=" + Id_HISTORIQUE +
                ", valeur=" + valeur +
                ", date=" + date +
                ", heure=" + heure +
                ", before_after=" + before_after +
                ", Id_UTILISATEUR=" + Id_UTILISATEUR +
                '}';
    }

    public static String add(Historique historique) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("Historique", errorMsg);
            return errorMsg;
        }

        String sql = "INSERT INTO `historique` (`valeur`, `date`, `heure`, `before_after`, `Id_UTILISATEUR`) VALUES (?, ?, ?, ?, ?)";
        try {
            PreparedStatement stmt = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setDouble(1, historique.getValeur());
            stmt.setString(2, historique.getDate());
            stmt.setString(3, historique.getHeure());
            stmt.setString(4, historique.getBefore_after());
            stmt.setInt(5, historique.getId_UTILISATEUR());

            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    historique.setId_HISTORIQUE(rs.getInt(1));
                }
                return "Historique ajouté avec succès";
            } else {
                return "Échec de l'ajout de l'historique";
            }
        } catch (Exception e) {
            Log.e("Historique", "Error adding historique: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static String Delete(Historique historique) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("Historique", errorMsg);
            return errorMsg;
        }

        String sql = "DELETE FROM `historique` WHERE Id_HISTORIQUE = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, historique.getId_HISTORIQUE());
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "Historique supprimé avec succès" : "Historique non trouvé";
        } catch (Exception e) {
            Log.e("Historique", "Error deleting historique: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }

    public static ArrayList<Historique> Chercher_Id_HISTORIQUE(String id) {
        ArrayList<Historique> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("Historique", "Database connection failed in Chercher_Id_HISTORIQUE. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `historique` WHERE Id_HISTORIQUE LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, id + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Historique historique = new Historique(
                        rs.getInt("Id_HISTORIQUE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(historique);
            }
        } catch (Exception e) {
            Log.e("Historique", "Error searching by Id_HISTORIQUE: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<Historique> Chercher_Valeur(String valeur) {
        ArrayList<Historique> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("Historique", "Database connection failed in Chercher_Valeur. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `historique` WHERE valeur LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, valeur + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Historique historique = new Historique(
                        rs.getInt("Id_HISTORIQUE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(historique);
            }
        } catch (Exception e) {
            Log.e("Historique", "Error searching by valeur: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<Historique> Chercher_Date(String date) {
        ArrayList<Historique> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("Historique", "Database connection failed in Chercher_Date. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `historique` WHERE date LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, date + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Historique historique = new Historique(
                        rs.getInt("Id_HISTORIQUE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(historique);
            }
        } catch (Exception e) {
            Log.e("Historique", "Error searching by date: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<Historique> Chercher_Heure(String heure) {
        ArrayList<Historique> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("Historique", "Database connection failed in Chercher_Heure. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `historique` WHERE heure LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, heure + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Historique historique = new Historique(
                        rs.getInt("Id_HISTORIQUE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(historique);
            }
        } catch (Exception e) {
            Log.e("Historique", "Error searching by heure: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<Historique> Chercher_Before_After(String beforeAfter) {
        ArrayList<Historique> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("Historique", "Database connection failed in Chercher_Before_After. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `historique` WHERE before_after LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, beforeAfter + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Historique historique = new Historique(
                        rs.getInt("Id_HISTORIQUE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(historique);
            }
        } catch (Exception e) {
            Log.e("Historique", "Error searching by before_after: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static ArrayList<Historique> Chercher_Id_UTILISATEUR(String userId) {
        ArrayList<Historique> result = new ArrayList<>();
        Shared.connecter();
        if (con == null) {
            Log.e("Historique", "Database connection failed in Chercher_Id_UTILISATEUR. Détails: " + Shared.getLastErrorMessage());
            return result;
        }

        String sql = "SELECT * FROM `historique` WHERE Id_UTILISATEUR LIKE ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setString(1, userId + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Historique historique = new Historique(
                        rs.getInt("Id_HISTORIQUE"),
                        rs.getDouble("valeur"),
                        rs.getString("date"),
                        rs.getString("heure"),
                        rs.getString("before_after"),
                        rs.getInt("Id_UTILISATEUR")
                );
                result.add(historique);
            }
        } catch (Exception e) {
            Log.e("Historique", "Error searching by Id_UTILISATEUR: " + e.getMessage(), e);
        } finally {
            Shared.deconnecter();
        }
        return result;
    }

    public static String Modifier_Historique(Historique historique) {
        Shared.connecter();
        if (con == null) {
            String errorMsg = "Erreur: Connexion à la base de données échouée. Détails: " + Shared.getLastErrorMessage();
            Log.e("Historique", errorMsg);
            return errorMsg;
        }

        String sql = "UPDATE `historique` SET `valeur` = ?, `date` = ?, `heure` = ?, `before_after` = ?, `Id_UTILISATEUR` = ? WHERE `Id_HISTORIQUE` = ?";
        try {
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setDouble(1, historique.getValeur());
            stmt.setString(2, historique.getDate());
            stmt.setString(3, historique.getHeure());
            stmt.setString(4, historique.getBefore_after());
            stmt.setInt(5, historique.getId_UTILISATEUR());
            stmt.setInt(6, historique.getId_HISTORIQUE());

            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0 ? "Historique sauvegardé avec succès" : "Historique non trouvé";
        } catch (Exception e) {
            Log.e("Historique", "Error updating historique: " + e.getMessage(), e);
            return "Erreur: " + e.getMessage();
        } finally {
            Shared.deconnecter();
        }
    }
}