<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/gradient_background">

    <!-- Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Conseils pour les diabétiques"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:paddingBottom="20dp"
        android:textColor="#000000"/>

    <!-- Button for Type 1 Diabetes -->
    <Button
        android:id="@+id/btnType1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Conseils pour le diabète de type 1"
        android:textSize="18sp"
        android:backgroundTint="#4CAF50"
        android:layout_marginBottom="10dp"/>

    <!-- Button for Type 2 Diabetes -->
    <Button
        android:id="@+id/btnType2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Conseils pour le diabète de type 2"
        android:textSize="18sp"
        android:backgroundTint="#2196F3"/>

    <!-- TextView to display tips -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="20dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="8dp"
            android:text="Choisissez un type de diabète pour afficher les conseils"
            android:textColor="#333333"
            android:textSize="16sp" />
    </ScrollView>

</LinearLayout>