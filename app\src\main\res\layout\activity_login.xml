<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@drawable/pfc"
    android:padding="16dp"
    tools:context=".LoginActivity">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="#FFFFFF">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:id="@+id/LoginText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="تسجيل الدخول"
                android:textAlignment="center"
                android:textColor="@color/purple_500"
                android:textSize="32sp"
                android:textStyle="bold"
                android:paddingBottom="20dp" />

            <EditText
                android:id="@+id/email_input"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/custom_edittext"
                android:drawableStart="@drawable/baseline_person_24"
                android:drawablePadding="10dp"
                android:hint="البريد الإلكتروني"
                android:inputType="textEmailAddress"
                android:padding="12dp"
                android:textColor="@color/black"
                android:textColorHint="#757575"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/password_input"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/custom_edittext"
                android:drawableStart="@drawable/baseline_lock_24"
                android:drawablePadding="10dp"
                android:hint="كلمة المرور"
                android:inputType="textPassword"
                android:padding="12dp"
                android:textColor="@color/black"
                android:textColorHint="#757575"
                android:textSize="16sp" />

            <Button
                android:id="@+id/login_button"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="24dp"
                android:backgroundTint="@color/purple_500"
                android:text="تسجيل الدخول"
                android:textSize="18sp"
                android:textColor="#FFFFFF"
                android:letterSpacing="0.02" />

            <TextView
                android:id="@+id/forgotPasswordText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_gravity="end"
                android:text="نسيت كلمة المرور؟"
                android:textColor="@color/purple_500"
                android:textSize="14sp"
                android:padding="8dp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <Button
        android:id="@+id/register_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:padding="12dp"
        android:backgroundTint="@color/purple_500"
        android:text="ليس لديك حساب؟ سجل الآن"
        android:textColor="#FFFFFF"
        android:textSize="16sp" />

</LinearLayout>