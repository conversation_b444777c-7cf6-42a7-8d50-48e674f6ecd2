package com.example.myapp;

import android.annotation.SuppressLint;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import projet.UTILISATEUR;

public class UtilisateurActivity extends AppCompatActivity {

    private TextView nomText, prenomText, ageInput, sexText, typeDiabeteText, emailText;
    private Button backButton;
    private static final String TAG = "UtilisateurActivity";
    private Handler mainHandler = new Handler(Looper.getMainLooper());

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_utilusateur);

        // Initialize UI elements from XML
        nomText = findViewById(R.id.tvNom);
        prenomText = findViewById(R.id.tvPrenom);
        ageInput = findViewById(R.id.age_input);
        sexText = findViewById(R.id.tvSexe);
        typeDiabeteText = findViewById(R.id.tvDiabetesType);
        emailText = findViewById(R.id.tvEmail);
        backButton = findViewById(R.id.btnBack5);

        // Check for null views
        if (nomText == null || prenomText == null || ageInput == null ||
                sexText == null || typeDiabeteText == null || emailText == null || backButton == null) {
            Log.e(TAG, "One or more views are null. Check activity_utilisateur.xml for correct IDs.");
            Toast.makeText(this, "خطأ في تحميل الواجهة", Toast.LENGTH_LONG).show();
            finish();
            return;
        }

        // Load user data
        loadUserData();

        // Back button listener
        backButton.setOnClickListener(v -> finish());
    }

    private void loadUserData() {
        // Load from SharedPreferences
        SharedPreferences prefs = getSharedPreferences("UserPrefs", MODE_PRIVATE);
        String nom = prefs.getString("nom", "N/A");
        String infoSupp = prefs.getString("infoSupp", "N/A");
        String age = prefs.getString("age", "N/A");
        String sexe = prefs.getString("sexe", "N/A");
        String typeDiabete = prefs.getString("type_diabete", "N/A");
        String email = prefs.getString("email", "N/A");
        String motDePasse = prefs.getString("mot_de_passe", ""); // Needed for authentifier

        // Log loaded data
        Log.d(TAG, "Loaded from SharedPreferences: nom=" + nom + ", email=" + email +
                ", typeDiabete=" + typeDiabete + ", infoSupp=" + infoSupp +
                ", age=" + age + ", sexe=" + sexe);

        // Set initial data
        nomText.setText(nom);
        prenomText.setText(infoSupp);
        ageInput.setText(age);
        sexText.setText(sexe);
        typeDiabeteText.setText(typeDiabete);
        emailText.setText(email);

        // If key data is missing, try to fetch from database
        if ((age.equals("N/A") || sexe.equals("N/A") || infoSupp.equals("N/A")) && !email.equals("N/A") && !motDePasse.isEmpty()) {
            new Thread(() -> {
                try {
                    UTILISATEUR dbUser = UTILISATEUR.authentifier(email, motDePasse);
                    mainHandler.post(() -> {
                        if (dbUser != null) {
                            // Update SharedPreferences with fetched data
                            SharedPreferences.Editor editor = prefs.edit();
                            editor.putString("nom", dbUser.getNom() != null ? dbUser.getNom() : "N/A");
                            editor.putString("infoSupp", dbUser.getInformations_supplementaires() != null ? dbUser.getInformations_supplementaires() : "N/A");
                            editor.putString("age", dbUser.getÂge() != null ? dbUser.getÂge() : "N/A");
                            editor.putString("sexe", dbUser.getSexe() != null ? dbUser.getSexe() : "N/A");
                            editor.putString("type_diabete", dbUser.getType_diabète() != null ? dbUser.getType_diabète() : "N/A");
                            editor.putString("email", dbUser.getEmail() != null ? dbUser.getEmail() : "N/A");
                            editor.apply();

                            // Update UI
                            nomText.setText(dbUser.getNom() != null ? dbUser.getNom() : "N/A");
                            prenomText.setText(dbUser.getInformations_supplementaires() != null ? dbUser.getInformations_supplementaires() : "N/A");
                            ageInput.setText(dbUser.getÂge() != null ? dbUser.getÂge() : "N/A");
                            sexText.setText(dbUser.getSexe() != null ? dbUser.getSexe() : "N/A");
                            typeDiabeteText.setText(dbUser.getType_diabète() != null ? dbUser.getType_diabète() : "N/A");
                            emailText.setText(dbUser.getEmail() != null ? dbUser.getEmail() : "N/A");

                            Log.d(TAG, "Updated UI with database data: " + dbUser.toString());
                        } else {
                            Log.w(TAG, "No user found in database for email: " + email);
                            Toast.makeText(this, "تعذر تحميل بيانات المستخدم من قاعدة البيانات", Toast.LENGTH_SHORT).show();
                        }
                    });
                } catch (Exception e) {
                    Log.e(TAG, "Database error: " + e.getMessage(), e);
                    mainHandler.post(() -> {
                        Toast.makeText(this, "خطأ في الاتصال بقاعدة البيانات: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                }
            }).start();
        } else if (nom.equals("N/A") && email.equals("N/A") && typeDiabete.equals("N/A")) {
            Log.w(TAG, "No user data found in SharedPreferences");
            Toast.makeText(this, "لا توجد بيانات مستخدم متاحة", Toast.LENGTH_SHORT).show();
        }
    }
}