/*
 * Aligned with AlimentationActivity and activity_alimentation.xml for real-time food search.
 * Interacts with MySQL table 'Alimentation' (food_name, calories, carbohydrates, sugar, protein).
 */
package projet;

import android.util.Log;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a food item with nutritional information.
 */
public class Alimentation {
    private String foodName;
    private double calories;
    private double carbohydrates;
    private double sugar;
    private double protein;
    private static final String TAG = "Alimentation";

    // Constructors
    public Alimentation(String foodName) {
        this.foodName = foodName;
    }

    public Alimentation(String foodName, double calories, double carbohydrates, double sugar, double protein) {
        this.foodName = foodName;
        this.calories = calories;
        this.carbohydrates = carbohydrates;
        this.sugar = sugar;
        this.protein = protein;
    }

    // Getters and Setters
    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public double getCalories() {
        return calories;
    }

    public void setCalories(double calories) {
        this.calories = calories;
    }

    public double getCarbohydrates() {
        return carbohydrates;
    }

    public void setCarbohydrates(double carbohydrates) {
        this.carbohydrates = carbohydrates;
    }

    public double getSugar() {
        return sugar;
    }

    public void setSugar(double sugar) {
        this.sugar = sugar;
    }

    public double getProtein() {
        return protein;
    }

    public void setProtein(double protein) {
        this.protein = protein;
    }

    @Override
    public String toString() {
        return String.format(
                "Aliment : %s\nCalories : %.1f kcal\nGlucides : %.1f g\nSucres : %.1f g\nProtéines : %.1f g",
                foodName, calories, carbohydrates, sugar, protein);
    }


    // Add a food item
    public boolean addAlimentation() {
        Shared.connecter();
        String sql = "INSERT INTO Alimentation (food_name, calories, carbohydrates, sugar, protein) VALUES (?, ?, ?, ?, ?)";
        try (PreparedStatement stmt = Shared.con.prepareStatement(sql)) {
            stmt.setString(1, foodName);
            stmt.setDouble(2, calories);
            stmt.setDouble(3, carbohydrates);
            stmt.setDouble(4, sugar);
            stmt.setDouble(5, protein);
            stmt.executeUpdate();
            Log.d(TAG, "Food added: " + foodName);
            return true;
        } catch (SQLException e) {
            Log.e(TAG, "Error adding food: " + e.getMessage());
            return false;
        } finally {
            Shared.deconnecter();
        }
    }

    // Search for a food by name (exact or partial match)
    public static Alimentation searchByFoodName(String foodName) {
        Shared.connecter();
        String sql = "SELECT food_name, calories, carbohydrates, sugar, protein FROM Alimentation WHERE food_name = ? OR food_name LIKE ?";
        try (PreparedStatement stmt = Shared.con.prepareStatement(sql)) {
            stmt.setString(1, foodName);
            stmt.setString(2, foodName + "%");
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                Alimentation food = new Alimentation(
                        rs.getString("food_name"),
                        rs.getDouble("calories"),
                        rs.getDouble("carbohydrates"),
                        rs.getDouble("sugar"),
                        rs.getDouble("protein")
                );
                Log.d(TAG, "Found food: " + food.getFoodName());
                return food;
            }
            Log.d(TAG, "No food found for: " + foodName);
        } catch (SQLException e) {
            Log.e(TAG, "Error searching food: " + e.getMessage());
        } finally {
            Shared.deconnecter();
        }
        return null;
    }

    // Get all foods
    public static List<Alimentation> getAll() {
        Shared.connecter();
        List<Alimentation> list = new ArrayList<>();
        String sql = "SELECT food_name, calories, carbohydrates, sugar, protein FROM Alimentation";
        try (PreparedStatement stmt = Shared.con.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            while (rs.next()) {
                Alimentation food = new Alimentation(
                        rs.getString("food_name"),
                        rs.getDouble("calories"),
                        rs.getDouble("carbohydrates"),
                        rs.getDouble("sugar"),
                        rs.getDouble("protein")
                );
                list.add(food);
            }
            Log.d(TAG, "Fetched " + list.size() + " foods");
        } catch (SQLException e) {
            Log.e(TAG, "Error fetching all foods: " + e.getMessage());
        } finally {
            Shared.deconnecter();
        }
        return list;
    }

    // Delete a food by name
    public static boolean deleteByNom(String nom) {
        Shared.connecter();
        String sql = "DELETE FROM Alimentation WHERE food_name = ?";
        try (PreparedStatement stmt = Shared.con.prepareStatement(sql)) {
            stmt.setString(1, nom);
            boolean deleted = stmt.executeUpdate() > 0;
            Log.d(TAG, deleted ? "Food deleted: " + nom : "No food found to delete: " + nom);
            return deleted;
        } catch (SQLException e) {
            Log.e(TAG, "Error deleting food: " + e.getMessage());
            return false;
        } finally {
            Shared.deconnecter();
        }
    }
}