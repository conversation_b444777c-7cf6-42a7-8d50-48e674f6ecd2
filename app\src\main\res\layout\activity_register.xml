<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    tools:context=".RegisterActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="16dp">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="#FFFFFF">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="center_horizontal"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="تسجيل حساب جديد"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textAlignment="center"
                    android:textColor="@color/purple_500"
                    android:layout_marginBottom="20dp"/>

                <!-- الاسم -->
                <EditText
                    android:id="@+id/nom_input"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/custom_edittext"
                    android:drawableStart="@drawable/baseline_person_24"
                    android:drawablePadding="10dp"
                    android:hint="الاسم"
                    android:padding="12dp"
                    android:textColor="@color/black"
                    android:textColorHint="#757575"
                    android:textSize="16sp" />

                <!-- معلومات إضافية -->
                <EditText
                    android:id="@+id/informations_supplementaires_input"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="@drawable/custom_edittext"
                    android:drawableStart="@drawable/baseline_person_24"
                    android:drawablePadding="10dp"
                    android:hint="معلومات إضافية"
                    android:padding="12dp"
                    android:textColor="@color/black"
                    android:textColorHint="#757575"
                    android:textSize="16sp"
                    android:layout_marginBottom="16dp"/>

                <!-- العمر -->
                <EditText
                    android:id="@+id/age_input"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="@drawable/custom_edittext"
                    android:drawableStart="@android:drawable/ic_menu_today"
                    android:drawablePadding="10dp"
                    android:hint="العمر"
                    android:inputType="number"
                    android:padding="12dp"
                    android:textColor="@color/black"
                    android:textColorHint="#757575"
                    android:textSize="16sp"
                    android:layout_marginBottom="16dp"/>

                <!-- الجنس -->
                <Spinner
                    android:id="@+id/spinnerSexe"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/custom_edittext"
                    android:drawableLeft="@drawable/baseline_swap_horiz_24"
                    android:hint="sexe"

                    android:padding="8dp"
                    android:layout_marginBottom="16dp"
                    android:spinnerMode="dropdown"/>

                <!-- نوع السكري -->
                <Spinner
                    android:id="@+id/spinnerDiabetesType"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/custom_edittext"
                    android:drawableLeft="@drawable/baseline_swap_horiz_24"
                    android:hint="Diabete"
                    android:padding="8dp"
                    android:layout_marginBottom="16dp"
                    android:spinnerMode="dropdown"/>


                <!-- الحمل (يظهر فقط إذا تم اختيار أنثى) -->
                <LinearLayout
                    android:id="@+id/pregnancy_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="هل أنتِ حامل؟"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:paddingBottom="8dp"/>

                </LinearLayout>


                <!-- البريد الإلكتروني -->
                <EditText
                    android:id="@+id/email_input"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="@drawable/custom_edittext"
                    android:drawableStart="@android:drawable/ic_dialog_email"
                    android:drawablePadding="10dp"
                    android:hint="البريد الإلكتروني"
                    android:inputType="textEmailAddress"
                    android:padding="12dp"
                    android:textColor="@color/black"
                    android:textColorHint="#757575"
                    android:textSize="16sp"
                    android:layout_marginBottom="16dp"/>

                <!-- كلمة المرور -->
                <EditText
                    android:id="@+id/mot_de_passe_input"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="@drawable/custom_edittext"
                    android:drawableStart="@android:drawable/ic_lock_idle_lock"
                    android:drawablePadding="10dp"
                    android:hint="كلمة المرور"
                    android:inputType="textPassword"
                    android:padding="12dp"
                    android:textColor="@color/black"
                    android:textColorHint="#757575"
                    android:textSize="16sp"
                    android:layout_marginBottom="24dp"/>

                <!-- زر التسجيل -->
                <Button
                    android:id="@+id/register_button"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:backgroundTint="@color/purple_500"
                    android:text="تسجيل"
                    android:textSize="18sp"
                    android:textColor="#FFFFFF"
                    android:letterSpacing="0.02"
                    android:layout_marginBottom="16dp"/>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/login_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="لديك حساب؟ تسجيل الدخول"
            android:textSize="16sp"
            android:textColor="@color/purple_500"
            android:layout_gravity="center"
            android:padding="12dp"/>

    </LinearLayout>
</ScrollView>